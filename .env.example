# =====================================================
# WhatsApp Integration Configuration for SQL Server Express
# =====================================================

# WhatsApp Business API Configuration
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token_here
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id_here
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id_here
WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token_here
WHATSAPP_APP_SECRET=your_app_secret_for_signature_verification

# SQL Server Express Database Configuration
# Вариант 1: Подключение с SQL Server Authentication
ACCESS_DSN=DRIVER={ODBC Driver 17 for SQL Server};SERVER=*************,1433;DATABASE=whatsapp_integration;UID=WhatsApp_ferroli;PWD=YourStrongPassword123!

# Вариант 2: Подключение с Windows Authentication (закомментировано)
# ACCESS_DSN=DRIVER={ODBC Driver 17 for SQL Server};SERVER=localhost\SQLEXPRESS;DATABASE=whatsapp_integration;Trusted_Connection=yes

# Вариант 3: Подключение к удаленному SQL Server
# ACCESS_DSN=DRIVER={ODBC Driver 17 for SQL Server};SERVER=*************\SQLEXPRESS;DATABASE=whatsapp_integration;UID=whatsapp_user;PWD=SecurePassword123!

# Security Configuration
ACCESS_API_KEY=your_secure_api_key_for_send_endpoint_here
WEBHOOK_SECRET=your_webhook_secret_for_additional_security

# Flask Server Configuration
PORT=5000
FLASK_ENV=production
FLASK_DEBUG=False
DEBUG=False

# Performance & Limits
MAX_CONTENT_LENGTH=2097152
WEBHOOK_TIMEOUT=30
RATE_LIMIT_PER_MINUTE=60

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=whatsapp_webhook.log
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5

# Auto-reply Configuration
AUTO_REPLY_ENABLED=False
AUTO_REPLY_MESSAGE=Спасибо за сообщение! Мы ответим в ближайшее время.

# Maintenance Configuration
LOG_RETENTION_DAYS=30
CLEANUP_INTERVAL_HOURS=24
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Скрипт для просмотра данных в тестовой базе данных SQLite
"""

import sqlite3
import os
from datetime import datetime

def view_database():
    """Просмотр содержимого базы данных"""
    db_path = 'whatsapp_test.db'
    
    if not os.path.exists(db_path):
        print(f"❌ База данных не найдена: {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    print(f"📊 Просмотр базы данных: {db_path}")
    print("=" * 60)
    
    # Просмотр сообщений
    print("\n📨 ВХОДЯЩИЕ СООБЩЕНИЯ:")
    cursor.execute("SELECT * FROM Messages ORDER BY SentAt DESC")
    messages = cursor.fetchall()
    
    if messages:
        for msg in messages:
            print(f"  ID: {msg['MessageID']}")
            print(f"  От: {msg['FromUser']} → Кому: {msg['ToUser']}")
            print(f"  Текст: {msg['Text']}")
            print(f"  Тип: {msg['MessageType']}")
            print(f"  Время: {msg['SentAt']}")
            print(f"  Статус: {msg['Status']}")
            print("  " + "-" * 40)
    else:
        print("  Нет сообщений")
    
    # Просмотр исходящих сообщений
    print("\n📤 ИСХОДЯЩИЕ СООБЩЕНИЯ:")
    cursor.execute("SELECT * FROM OutgoingMessages ORDER BY CreatedAt DESC")
    outgoing = cursor.fetchall()
    
    if outgoing:
        for msg in outgoing:
            print(f"  ID: {msg['ID']}")
            print(f"  Кому: {msg['ToUser']}")
            print(f"  Текст: {msg['MessageText']}")
            print(f"  Статус: {msg['Status']}")
            print(f"  Создано: {msg['CreatedAt']}")
            if msg['WhatsAppMessageID']:
                print(f"  WhatsApp ID: {msg['WhatsAppMessageID']}")
            print("  " + "-" * 40)
    else:
        print("  Нет исходящих сообщений")
    
    # Просмотр контактов
    print("\n👥 КОНТАКТЫ:")
    cursor.execute("SELECT * FROM Contacts ORDER BY LastMessageAt DESC")
    contacts = cursor.fetchall()
    
    if contacts:
        for contact in contacts:
            print(f"  Телефон: {contact['PhoneNumber']}")
            print(f"  Имя: {contact['ContactName']}")
            print(f"  Компания: {contact['Company']}")
            print(f"  Сообщений: {contact['MessageCount']}")
            print(f"  Последнее сообщение: {contact['LastMessageAt']}")
            print(f"  Активен: {'Да' if contact['IsActive'] else 'Нет'}")
            print("  " + "-" * 40)
    else:
        print("  Нет контактов")
    
    # Просмотр настроек
    print("\n⚙️ НАСТРОЙКИ:")
    cursor.execute("SELECT * FROM Settings ORDER BY SettingKey")
    settings = cursor.fetchall()
    
    if settings:
        for setting in settings:
            print(f"  {setting['SettingKey']}: {setting['SettingValue']}")
            if setting['Description']:
                print(f"    ({setting['Description']})")
    else:
        print("  Нет настроек")
    
    # Просмотр логов
    print("\n📋 ЛОГИ (последние 10):")
    cursor.execute("SELECT * FROM Logs ORDER BY CreatedAt DESC LIMIT 10")
    logs = cursor.fetchall()
    
    if logs:
        for log in logs:
            print(f"  [{log['LogLevel']}] {log['CreatedAt']}")
            print(f"    {log['Message']}")
            if log['Source']:
                print(f"    Источник: {log['Source']}")
            print("  " + "-" * 30)
    else:
        print("  Нет логов")
    
    # Статистика
    print("\n📈 СТАТИСТИКА:")
    cursor.execute("SELECT COUNT(*) as count FROM Messages")
    msg_count = cursor.fetchone()['count']
    
    cursor.execute("SELECT COUNT(*) as count FROM OutgoingMessages")
    out_count = cursor.fetchone()['count']
    
    cursor.execute("SELECT COUNT(*) as count FROM Contacts")
    contact_count = cursor.fetchone()['count']
    
    cursor.execute("SELECT COUNT(*) as count FROM Logs")
    log_count = cursor.fetchone()['count']
    
    print(f"  Входящих сообщений: {msg_count}")
    print(f"  Исходящих сообщений: {out_count}")
    print(f"  Контактов: {contact_count}")
    print(f"  Записей в логах: {log_count}")
    
    conn.close()
    print("\n✅ Просмотр завершен")

if __name__ == '__main__':
    view_database()
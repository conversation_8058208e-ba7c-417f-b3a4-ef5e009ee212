-- =====================================================
-- Примеры данных для тестирования таблиц
-- =====================================================

-- Добавление тестовых контактов
INSERT INTO Contacts (PhoneNumber, ContactName, Company, Email, Notes) VALUES
('79001234567', 'Иван Петров', 'ООО Технологии', '<EMAIL>', 'Клиент с сайта'),
('79009876543', 'Мария Сидорова', 'ИП Сидорова', '<EMAIL>', 'Постоя<PERSON><PERSON><PERSON>й клиент'),
('79005554433', 'А<PERSON><PERSON>к<PERSON><PERSON>й Козлов', 'АО Инновации', '<EMAIL>', 'Новый клиент'),
('79001112233', 'Елена Воробьева', 'ООО Торговля', '<EMAIL>', 'Интересовалась ценами');

-- Добавление тестовых входящих сообщений
INSERT INTO Messages (MessageID, ChatID, FromUser, ToUser, Text, MessageType, SentAt, Status) VALUES
('msg_001', 'chat_001', '79001234567', '79000000000', 'Здравствуйте! Интересует ваша продукция', 'text', '2024-01-15 10:30:00', 'received'),
('msg_002', 'chat_001', '79001234567', '79000000000', 'Какие у вас цены?', 'text', '2024-01-15 10:32:00', 'received'),
('msg_003', 'chat_002', '79009876543', '79000000000', 'Спасибо за быструю доставку!', 'text', '2024-01-15 11:15:00', 'received'),
('msg_004', 'chat_003', '79005554433', '79000000000', 'Можете перезвонить?', 'text', '2024-01-15 12:00:00', 'received'),
('msg_005', 'chat_004', '79001112233', '79000000000', 'Есть ли скидки для оптовых заказов?', 'text', '2024-01-15 14:20:00', 'received');

-- Добавление тестовых исходящих сообщений
INSERT INTO OutgoingMessages (ToUser, MessageText, MessageType, Status, CreatedAt) VALUES
('79001234567', 'Добрый день! Спасибо за обращение. Наш менеджер свяжется с вами в течение часа.', 'text', 'sent', '2024-01-15 10:35:00'),
('79009876543', 'Рады, что вы довольны! Обращайтесь еще.', 'text', 'sent', '2024-01-15 11:20:00'),
('79005554433', 'Конечно! Перезвоним в течение 15 минут.', 'text', 'sent', '2024-01-15 12:05:00'),
('79001112233', 'Да, у нас есть специальные условия для оптовиков. Отправлю прайс-лист.', 'text', 'pending', '2024-01-15 14:25:00');

-- Добавление тестовых логов
INSERT INTO Logs (LogLevel, Message, Source) VALUES
('INFO', 'Webhook получен от WhatsApp', 'webhook'),
('INFO', 'Сообщение сохранено в базу: msg_001', 'webhook'),
('WARNING', 'Неизвестный тип сообщения: location', 'webhook'),
('ERROR', 'Ошибка подключения к базе данных', 'database'),
('INFO', 'Отправлено сообщение: ID=1', 'sender');

-- Обновление статистики контактов
UPDATE Contacts SET 
    LastMessageAt = '2024-01-15 10:32:00',
    MessageCount = 2
WHERE PhoneNumber = '79001234567';

UPDATE Contacts SET 
    LastMessageAt = '2024-01-15 11:15:00',
    MessageCount = 1
WHERE PhoneNumber = '79009876543';

UPDATE Contacts SET 
    LastMessageAt = '2024-01-15 12:00:00',
    MessageCount = 1
WHERE PhoneNumber = '79005554433';

UPDATE Contacts SET 
    LastMessageAt = '2024-01-15 14:20:00',
    MessageCount = 1
WHERE PhoneNumber = '79001112233';

-- =====================================================
-- Примеры запросов для тестирования
-- =====================================================

-- Последние сообщения
-- SELECT * FROM vw_RecentMessages;

-- Непрочитанные сообщения
-- SELECT * FROM vw_UnreadMessages;

-- Статистика контактов
-- SELECT * FROM vw_ContactStats;

-- Сообщения конкретного пользователя
-- SELECT * FROM Messages WHERE FromUser = '79001234567' ORDER BY SentAt DESC;

-- Исходящие сообщения в статусе pending
-- SELECT * FROM OutgoingMessages WHERE Status = 'pending' ORDER BY CreatedAt;

-- Логи ошибок
-- SELECT * FROM Logs WHERE LogLevel = 'ERROR' ORDER BY CreatedAt DESC; 
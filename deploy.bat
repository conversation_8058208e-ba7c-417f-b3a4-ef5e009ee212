@echo off
echo ========================================
echo Развертывание WhatsApp Access Integration
echo ========================================

echo.
echo 1. Проверка Python...
python --version
if errorlevel 1 (
    echo ОШИБКА: Python не установлен!
    pause
    exit /b 1
)

echo.
echo 2. Установка зависимостей...
pip install -r requirements.txt
if errorlevel 1 (
    echo ОШИБКА: Не удалось установить зависимости!
    pause
    exit /b 1
)

echo.
echo 3. Проверка ODBC драйвера...
python -c "import pyodbc; print('ODBC драйвер доступен')"
if errorlevel 1 (
    echo ПРЕДУПРЕЖДЕНИЕ: ODBC драйвер не найден. Установите Microsoft Access Database Engine.
)

echo.
echo 4. Создание файла конфигурации...
if not exist .env (
    copy config.env.example .env
    echo Создан файл .env. Отредактируйте его перед запуском!
)

echo.
echo 5. Проверка структуры базы данных...
echo Убедитесь, что таблицы созданы в Access/SQL Server
echo Выполните скрипт: whatsapp_access_tables.sql

echo.
echo ========================================
echo Развертывание завершено!
echo ========================================
echo.
echo Следующие шаги:
echo 1. Отредактируйте файл .env
echo 2. Создайте таблицы в базе данных
echo 3. Настройте ODBC DSN
echo 4. Запустите сервер: python webhook_server.py
echo.
pause 
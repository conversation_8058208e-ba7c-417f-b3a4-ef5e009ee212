#!/bin/bash

# Скрипт для загрузки и развертывания WhatsApp Webhook на удаленном сервере
# Использование: ./upload_and_deploy.sh [server_ip]

set -e

SERVER_IP="${1:-*************}"
SERVER_USER="root"
PROJECT_DIR="/opt/whatsapp-webhook"

echo "=== Загрузка и развертывание на сервер $SERVER_IP ==="

# Проверка доступности сервера
echo "Проверка доступности сервера..."
if ! ping -c 1 $SERVER_IP > /dev/null 2>&1; then
    echo "Ошибка: Сервер $SERVER_IP недоступен"
    exit 1
fi

# Загрузка скрипта развертывания
echo "Загрузка скрипта развертывания..."
scp deploy_remote.sh $SERVER_USER@$SERVER_IP:/tmp/

# Выполнение развертывания на сервере
echo "Выполнение развертывания на сервере..."
ssh $SERVER_USER@$SERVER_IP "chmod +x /tmp/deploy_remote.sh && /tmp/deploy_remote.sh"

# Загрузка основного кода приложения
echo "Загрузка основного кода приложения..."
scp webhook_server.py $SERVER_USER@$SERVER_IP:/tmp/

# Обновление кода на сервере
echo "Обновление кода на сервере..."
ssh $SERVER_USER@$SERVER_IP "$PROJECT_DIR/upload_code.sh /tmp/webhook_server.py"

# Проверка статуса
echo "Проверка статуса развертывания..."
ssh $SERVER_USER@$SERVER_IP "$PROJECT_DIR/status.sh"

echo ""
echo "=== Развертывание завершено успешно ==="
echo "Сервер доступен по адресу: http://$SERVER_IP"
echo "Тест подключения к базе: http://$SERVER_IP/db-test"
echo ""
echo "Для подключения к серверу: ssh $SERVER_USER@$SERVER_IP"
echo "Для проверки логов: ssh $SERVER_USER@$SERVER_IP 'tail -f /var/log/whatsapp-webhook.log'"
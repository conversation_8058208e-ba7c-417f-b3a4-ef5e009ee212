﻿import os
import pyodbc
import requests
import logging
from flask import Flask, request, abort
from dotenv import load_dotenv
from datetime import datetime
from logging.handlers import RotatingFileHandler
import threading
import time

# Загрузка переменных окружения из .env
load_dotenv()

# Настройки для SQL Server
SQL_SERVER = os.getenv("SQL_SERVER", "localhost\\SQLEXPRESS")
SQL_DATABASE = os.getenv("SQL_DATABASE", "WhatsAppDB")
SQL_USERNAME = os.getenv("SQL_USERNAME", "")
SQL_PASSWORD = os.getenv("SQL_PASSWORD", "")
USE_WINDOWS_AUTH = os.getenv("USE_WINDOWS_AUTH", "True").lower() == "true"

VERIFY_TOKEN = os.getenv("VERIFY_TOKEN")
MEDIA_TOKEN = os.getenv("MEDIA_TOKEN")
ALLOWED_IPS = os.getenv("ALLOWED_IPS", "").split(",")

# Настройка логирования
log_handler = RotatingFileHandler("whatsapp_integration.log", maxBytes=10*1024*1024, backupCount=5)
logging.basicConfig(handlers=[log_handler], level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")

def get_connection_string():
    """Формирует строку подключения к SQL Server"""
    if USE_WINDOWS_AUTH:
        return (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"
            f"SERVER={SQL_SERVER};"
            f"DATABASE={SQL_DATABASE};"
            f"Trusted_Connection=yes;"
        )
    else:
        return (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"
            f"SERVER={SQL_SERVER};"
            f"DATABASE={SQL_DATABASE};"
            f"UID={SQL_USERNAME};"
            f"PWD={SQL_PASSWORD};"
        )

def get_db_connection():
    """Создаёт подключение к SQL Server с обработкой ошибок"""
    try:
        conn_str = get_connection_string()
        return pyodbc.connect(conn_str)
    except Exception as e:
        logging.error(f"Ошибка подключения к SQL Server: {e}")
        raise

def setup_database():
    """Создаёт таблицы если они не существуют"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Таблица сообщений
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Messages' AND xtype='U')
            CREATE TABLE Messages (
                ID INT IDENTITY(1,1) PRIMARY KEY,
                MessageID NVARCHAR(255) UNIQUE NOT NULL,
                ChatID NVARCHAR(255),
                FromUser NVARCHAR(255),
                Text NVARCHAR(MAX),
                Type NVARCHAR(50),
                MediaURL NVARCHAR(500),
                Timestamp DATETIME2,
                Direction NVARCHAR(20),
                CreatedAt DATETIME2 DEFAULT GETDATE()
            )
        """)
        
        # Таблица звонков
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Calls' AND xtype='U')
            CREATE TABLE Calls (
                ID INT IDENTITY(1,1) PRIMARY KEY,
                CallID NVARCHAR(255) UNIQUE NOT NULL,
                FromUser NVARCHAR(255),
                Direction NVARCHAR(20),
                Type NVARCHAR(50),
                Status NVARCHAR(50),
                Timestamp DATETIME2,
                CreatedAt DATETIME2 DEFAULT GETDATE()
            )
        """)
        
        # Индексы для быстрого поиска
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='IX_Messages_Timestamp')
            CREATE INDEX IX_Messages_Timestamp ON Messages (Timestamp DESC)
        """)
        
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='IX_Messages_FromUser')
            CREATE INDEX IX_Messages_FromUser ON Messages (FromUser)
        """)
        
        conn.commit()
        conn.close()
        logging.info("База данных инициализирована успешно")
        
    except Exception as e:
        logging.error(f"Ошибка инициализации базы данных: {e}")
        raise

def get_media_url(media_id):
    """Получает URL медиафайла от Meta API"""
    url = f"https://graph.facebook.com/v19.0/{media_id}"
    headers = {"Authorization": f"Bearer {MEDIA_TOKEN}"}
    try:
        res = requests.get(url, headers=headers, timeout=10)
        res.raise_for_status()
        media_info = res.json()
        return media_info.get("url")
    except Exception as e:
        logging.error(f"Ошибка получения media_url: {e}")
        return None

app = Flask(__name__)

@app.before_request
def limit_remote_addr():
    """Ограничение доступа по IP"""
    if ALLOWED_IPS and request.remote_addr not in ALLOWED_IPS:
        logging.warning(f"Попытка доступа с неразрешённого IP: {request.remote_addr}")
        abort(403)

@app.route('/health', methods=['GET'])
def health():
    """Healthcheck endpoint для мониторинга"""
    try:
        # Проверяем подключение к базе
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        cursor.fetchone()
        conn.close()
        return {"status": "OK", "database": "connected"}, 200
    except Exception as e:
        logging.error(f"Healthcheck failed: {e}")
        return {"status": "ERROR", "database": "disconnected"}, 500

@app.route('/stats', methods=['GET'])
def stats():
    """Статистика сообщений и звонков"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Количество сообщений за последние 24 часа
        cursor.execute("""
            SELECT COUNT(*) FROM Messages 
            WHERE Timestamp >= DATEADD(day, -1, GETDATE())
        """)
        messages_24h = cursor.fetchone()[0]
        
        # Количество звонков за последние 24 часа
        cursor.execute("""
            SELECT COUNT(*) FROM Calls 
            WHERE Timestamp >= DATEADD(day, -1, GETDATE())
        """)
        calls_24h = cursor.fetchone()[0]
        
        # Общее количество сообщений
        cursor.execute("SELECT COUNT(*) FROM Messages")
        total_messages = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            "messages_24h": messages_24h,
            "calls_24h": calls_24h,
            "total_messages": total_messages
        }, 200
        
    except Exception as e:
        logging.error(f"Ошибка получения статистики: {e}")
        return {"error": "Database error"}, 500

@app.route('/webhook', methods=['GET', 'POST'])
def webhook():
    """Основной webhook для приёма сообщений от WhatsApp Cloud API"""
    if request.method == 'GET':
        if request.args.get('hub.verify_token') == VERIFY_TOKEN:
            return request.args.get('hub.challenge')
        return 'Forbidden', 403

    # Проверка типа контента
    if not request.is_json:
        logging.warning("Некорректный Content-Type")
        return 'Bad Request', 400

    data = request.json
    logging.info(f"Получен webhook: {data}")

    try:
        conn = get_db_connection()
        cursor = conn.cursor()
    except Exception:
        return 'DB Error', 500

    try:
        for entry in data.get("entry", []):
            for change in entry.get("changes", []):
                value = change.get("value", {})

                # Обработка сообщений
                messages = value.get("messages", [])
                for msg in messages:
                    try:
                        msg_id = msg['id']
                        msg_type = msg['type']
                        from_user = msg['from']
                        text = msg.get("text", {}).get("body", "")
                        timestamp = datetime.utcfromtimestamp(int(msg["timestamp"]))
                        direction = "inbound"

                        media_url = None
                        if msg_type in ["image", "audio", "video", "document"]:
                            media_id = msg[msg_type]["id"]
                            media_url = get_media_url(media_id)

                        cursor.execute("""
                            INSERT INTO Messages (MessageID, ChatID, FromUser, Text, Type, MediaURL, Timestamp, Direction)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        """, msg_id, from_user, from_user, text, msg_type, media_url, timestamp, direction)
                        conn.commit()
                        logging.info(f"Сохранено сообщение {msg_id}")
                    except Exception as e:
                        logging.error(f"Ошибка при обработке сообщения: {e}")

                # Обработка звонков
                statuses = value.get("statuses", [])
                for status in statuses:
                    if status.get("type") == "call":
                        try:
                            call_id = status["id"]
                            call_type = status.get("call", {}).get("type")
                            call_status = status.get("call", {}).get("status")
                            from_user = status["recipient_id"]
                            timestamp = datetime.utcfromtimestamp(int(status["timestamp"]))
                            direction = "inbound" if from_user else "outbound"

                            cursor.execute("""
                                INSERT INTO Calls (CallID, FromUser, Direction, Type, Status, Timestamp)
                                VALUES (?, ?, ?, ?, ?, ?)
                            """, call_id, from_user, direction, call_type, call_status, timestamp)
                            conn.commit()
                            logging.info(f"Сохранён звонок {call_id}")
                        except Exception as e:
                            logging.error(f"Ошибка при обработке звонка: {e}")

    except Exception as e:
        logging.error(f"Ошибка обработки webhook: {e}")
        return 'Internal Error', 500
    finally:
        try:
            conn.close()
        except Exception:
            pass

    return '', 200

if __name__ == '__main__':
    # Инициализация базы данных при запуске
    setup_database()
    
    # Используй реальный SSL-сертификат в проде!
    app.run(host='0.0.0.0', port=443, ssl_context=('cert.pem', 'key.pem'))

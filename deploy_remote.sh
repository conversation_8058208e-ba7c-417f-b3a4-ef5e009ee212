#!/bin/bash

# Скрипт автоматического развертывания WhatsApp Webhook на удаленном сервере
# Использование: ./deploy_remote.sh

set -e

echo "=== Начало развертывания WhatsApp Webhook ==="

# Переменные
PROJECT_DIR="/opt/whatsapp-webhook"
SERVICE_NAME="whatsapp-webhook"
USER="whatsapp"

# Обновление системы
echo "Обновление системы..."
apt update && apt upgrade -y

# Установка необходимых пакетов
echo "Установка необходимых пакетов..."
apt install -y python3 python3-pip python3-venv nginx supervisor curl unixodbc unixodbc-dev

# Установка Microsoft ODBC Driver для SQL Server
echo "Установка Microsoft ODBC Driver для SQL Server..."
curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add -
curl https://packages.microsoft.com/config/ubuntu/22.04/prod.list > /etc/apt/sources.list.d/mssql-release.list
apt update
ACCEPT_EULA=Y apt install -y msodbcsql18

# Создание пользователя для приложения
echo "Создание пользователя приложения..."
if ! id "$USER" &>/dev/null; then
    useradd -r -s /bin/false -d $PROJECT_DIR $USER
fi

# Создание директории проекта
echo "Создание директории проекта..."
mkdir -p $PROJECT_DIR
chown $USER:$USER $PROJECT_DIR

# Создание виртуального окружения
echo "Создание виртуального окружения..."
sudo -u $USER python3 -m venv $PROJECT_DIR/venv

# Создание файла requirements.txt
echo "Создание requirements.txt..."
cat > $PROJECT_DIR/requirements.txt << 'EOF'
Flask==2.3.3
pyodbc==4.0.39
python-dotenv==1.0.0
requests==2.31.0
gunicorn==21.2.0
EOF

# Установка зависимостей Python
echo "Установка зависимостей Python..."
sudo -u $USER $PROJECT_DIR/venv/bin/pip install --upgrade pip
sudo -u $USER $PROJECT_DIR/venv/bin/pip install -r $PROJECT_DIR/requirements.txt

# Создание файла конфигурации окружения
echo "Создание файла конфигурации..."
cat > $PROJECT_DIR/.env << 'EOF'
# Строка подключения к SQL Server
ACCESS_DSN=DRIVER={ODBC Driver 18 for SQL Server};SERVER=*************,1433;DATABASE=whatsapp_integration;UID=WhatsApp_ferroli;PWD=YourStrongPassword123!;TrustServerCertificate=yes;

# WhatsApp API настройки
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_ACCESS_TOKEN=your_access_token
WHATSAPP_VERIFY_TOKEN=your_verify_token

# Flask настройки
FLASK_ENV=production
FLASK_DEBUG=False
EOF

chown $USER:$USER $PROJECT_DIR/.env
chmod 600 $PROJECT_DIR/.env

echo "Файл .env создан. Не забудьте обновить настройки WhatsApp API!"

# Создание основного файла приложения (будет заменен при загрузке)
echo "Создание заглушки приложения..."
cat > $PROJECT_DIR/webhook_server.py << 'EOF'
#!/usr/bin/env python3
from flask import Flask
app = Flask(__name__)

@app.route('/')
def hello():
    return "WhatsApp Webhook Server - готов к загрузке основного кода"

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
EOF

chown $USER:$USER $PROJECT_DIR/webhook_server.py

# Создание конфигурации Supervisor
echo "Создание конфигурации Supervisor..."
cat > /etc/supervisor/conf.d/$SERVICE_NAME.conf << EOF
[program:$SERVICE_NAME]
command=$PROJECT_DIR/venv/bin/gunicorn --bind 0.0.0.0:5000 --workers 2 webhook_server:app
directory=$PROJECT_DIR
user=$USER
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/$SERVICE_NAME.log
environment=PATH="$PROJECT_DIR/venv/bin"
EOF

# Создание конфигурации Nginx
echo "Создание конфигурации Nginx..."
cat > /etc/nginx/sites-available/$SERVICE_NAME << 'EOF'
server {
    listen 80;
    server_name _;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# Активация сайта Nginx
ln -sf /etc/nginx/sites-available/$SERVICE_NAME /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Тестирование конфигурации Nginx
nginx -t

# Перезапуск сервисов
echo "Перезапуск сервисов..."
systemctl reload supervisor
systemctl restart nginx
systemctl enable nginx

# Создание скрипта для загрузки кода
echo "Создание скрипта загрузки..."
cat > $PROJECT_DIR/upload_code.sh << 'EOF'
#!/bin/bash
# Скрипт для загрузки обновленного кода
# Использование: ./upload_code.sh webhook_server.py

if [ $# -eq 0 ]; then
    echo "Использование: $0 <файл_для_загрузки>"
    exit 1
fi

SOURCE_FILE="$1"
if [ ! -f "$SOURCE_FILE" ]; then
    echo "Файл $SOURCE_FILE не найден"
    exit 1
fi

# Остановка сервиса
supervisorctl stop whatsapp-webhook

# Копирование файла
cp "$SOURCE_FILE" /opt/whatsapp-webhook/webhook_server.py
chown whatsapp:whatsapp /opt/whatsapp-webhook/webhook_server.py

# Запуск сервиса
supervisorctl start whatsapp-webhook

echo "Код обновлен и сервис перезапущен"
supervisorctl status whatsapp-webhook
EOF

chmod +x $PROJECT_DIR/upload_code.sh

# Создание скрипта для проверки статуса
cat > $PROJECT_DIR/status.sh << 'EOF'
#!/bin/bash
echo "=== Статус WhatsApp Webhook ==="
echo "Supervisor:"
supervisorctl status whatsapp-webhook
echo ""
echo "Nginx:"
systemctl status nginx --no-pager -l
echo ""
echo "Логи приложения (последние 10 строк):"
tail -10 /var/log/whatsapp-webhook.log
echo ""
echo "Тест подключения к базе:"
curl -s http://localhost:5000/db-test | python3 -m json.tool
EOF

chmod +x $PROJECT_DIR/status.sh

# Запуск сервиса
echo "Запуск сервиса..."
supervisorctl reread
supervisorctl update
supervisorctl start $SERVICE_NAME

# Проверка статуса
echo "Проверка статуса сервиса..."
sleep 3
supervisorctl status $SERVICE_NAME

echo ""
echo "=== Развертывание завершено ==="
echo "Сервис доступен по адресу: http://$(hostname -I | awk '{print $1}')"
echo "Для проверки статуса: $PROJECT_DIR/status.sh"
echo "Для загрузки кода: $PROJECT_DIR/upload_code.sh <файл>"
echo ""
echo "Следующие шаги:"
echo "1. Обновите настройки WhatsApp API в файле $PROJECT_DIR/.env"
echo "2. Загрузите основной код: $PROJECT_DIR/upload_code.sh webhook_server.py"
echo "3. Проверьте работу: curl http://localhost:5000/db-test"
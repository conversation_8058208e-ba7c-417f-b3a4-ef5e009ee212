# Требования к интеграции WhatsApp с Microsoft Access

## Введение

Данная функция обеспечивает прямую интеграцию WhatsApp Business Cloud API с базой данных Microsoft Access для автоматического сохранения входящих сообщений, звонков и медиафайлов без использования сторонних сервисов типа Wazzup. Система включает промежуточный веб-сервис для получения webhook-уведомлений от Meta и запись данных в Access через ODBC-соединение.

## Требования

### Требование 1

**Пользовательская история:** Как администратор системы, я хочу настроить подключение к WhatsApp Business Cloud API, чтобы получать все входящие сообщения в реальном времени.

#### Критерии приемки

1. КОГДА система запускается ТОГДА она ДОЛЖНА успешно подключиться к Meta Business Manager
2. КОГДА приходит новое сообщение в WhatsApp ТОГДА система ДОЛЖНА получить webhook-уведомление в течение 5 секунд
3. ЕСЛИ токен доступа истекает ТОГДА система ДОЛЖНА уведомить администратора об ошибке аутентификации
4. КОГДА происходит верификация webhook ТОГДА система ДОЛЖНА корректно отвечать на challenge-запросы от Meta

### Требование 2

**Пользовательская история:** Как пользователь Access, я хочу видеть все входящие сообщения WhatsApp в базе данных, чтобы анализировать переписку с клиентами.

#### Критерии приемки

1. КОГДА приходит текстовое сообщение ТОГДА система ДОЛЖНА сохранить ID сообщения, номер отправителя, текст и время получения
2. КОГДА приходит медиафайл ТОГДА система ДОЛЖНА сохранить ссылку на файл и его тип (фото/аудио/видео/документ)
3. ЕСЛИ сообщение содержит эмодзи или специальные символы ТОГДА система ДОЛЖНА корректно сохранить их в UTF-8 кодировке
4. КОГДА сообщение сохраняется ТОГДА система ДОЛЖНА присвоить ему уникальный внутренний ID

### Требование 3

**Пользовательская история:** Как пользователь Access, я хочу видеть информацию о входящих звонках WhatsApp, чтобы отслеживать все обращения клиентов.

#### Критерии приемки

1. КОГДА поступает входящий звонок ТОГДА система ДОЛЖНА сохранить номер звонящего, время начала и статус звонка
2. КОГДА звонок завершается ТОГДА система ДОЛЖНА обновить запись с временем окончания и длительностью
3. ЕСЛИ звонок пропущен ТОГДА система ДОЛЖНА отметить его статус как "пропущенный"
4. КОГДА сохраняется информация о звонке ТОГДА система ДОЛЖНА связать его с соответствующим контактом

### Требование 4

**Пользовательская история:** Как администратор базы данных, я хочу иметь надежное ODBC-соединение с Access, чтобы данные сохранялись без потерь.

#### Критерии приемки

1. КОГДА система запускается ТОГДА она ДОЛЖНА проверить доступность ODBC-соединения с Access
2. ЕСЛИ соединение с базой прерывается ТОГДА система ДОЛЖНА автоматически переподключиться в течение 30 секунд
3. КОГДА происходит ошибка записи ТОГДА система ДОЛЖНА повторить попытку до 3 раз
4. ЕСЛИ база данных заблокирована ТОГДА система ДОЛЖНА ожидать разблокировки до 60 секунд

### Требование 5

**Пользовательская история:** Как разработчик, я хочу иметь промежуточный веб-сервис, который будет обрабатывать webhook-запросы от Meta и записывать данные в Access.

#### Критерии приемки

1. КОГДА сервис получает POST-запрос на /webhook ТОГДА он ДОЛЖЕН обработать JSON-данные от Meta
2. КОГДА сервис получает GET-запрос с hub.challenge ТОГДА он ДОЛЖЕН вернуть значение challenge для верификации
3. ЕСЛИ приходит некорректный JSON ТОГДА сервис ДОЛЖЕН вернуть HTTP 400 и записать ошибку в лог
4. КОГДА сервис обрабатывает событие ТОГДА он ДОЛЖЕН вернуть HTTP 200 в течение 20 секунд

### Требование 6

**Пользовательская история:** Как пользователь Access, я хочу видеть удобный интерфейс для просмотра сообщений и звонков, чтобы эффективно работать с данными.

#### Критерии приемки

1. КОГДА пользователь открывает форму сообщений ТОГДА система ДОЛЖНА показать последние 50 сообщений
2. КОГДА пользователь выбирает контакт ТОГДА система ДОЛЖНА отфильтровать сообщения только от этого номера
3. ЕСЛИ пользователь ищет по тексту ТОГДА система ДОЛЖНА найти все сообщения содержащие поисковый запрос
4. КОГДА пользователь открывает форму звонков ТОГДА система ДОЛЖНА показать список с сортировкой по времени

### Требование 7

**Пользовательская история:** Как администратор системы, я хочу иметь логирование и мониторинг работы интеграции, чтобы быстро выявлять и устранять проблемы.

#### Критерии приемки

1. КОГДА происходит любая операция ТОГДА система ДОЛЖНА записать событие в лог-файл с временной меткой
2. КОГДА возникает ошибка ТОГДА система ДОЛЖНА записать детальную информацию об ошибке
3. ЕСЛИ система не может подключиться к WhatsApp API ТОГДА она ДОЛЖНА отправить уведомление администратору
4. КОГДА лог-файл превышает 10 МБ ТОГДА система ДОЛЖНА создать новый файл и архивировать старый

### Требование 8

**Пользовательская история:** Как администратор безопасности, я хочу обеспечить защищенное хранение токенов доступа и конфиденциальных данных.

#### Критерии приемки

1. КОГДА система запускается ТОГДА она ДОЛЖНА загружать токены из зашифрованного файла конфигурации
2. ЕСЛИ токен доступа передается по сети ТОГДА соединение ДОЛЖНО использовать HTTPS
3. КОГДА сохраняются персональные данные ТОГДА номера телефонов ДОЛЖНЫ быть замаскированы в логах
4. ЕСЛИ происходит несанкционированный доступ ТОГДА система ДОЛЖНА заблокировать подключение на 24 часа

# Минимальный список данных для запроса у заказчика

1. **Номер WhatsApp**, который будет использоваться для интеграции (рабочий, не личный).
2. **Доступ к Facebook Business Manager** (или чтобы вас добавили как разработчика).
3. **Название компании и ИНН/сайт** — для прохождения верификации в Meta.
4. **Где будет работать промежуточный сервер?** (ПК/сервер с интернетом, Windows или Linux).
5. **Можно ли открыть порт 443 (HTTPS)?** — нужен для приёма webhook от Meta.
6. **Есть ли домен или статичный IP?** — чтобы Meta могла отправлять webhooks.
7. **Файл базы Access (.accdb)** или описание нужных таблиц/полей (если базы ещё нет).
8. **Есть ли пароль на базе?** — чтобы настроить ODBC-подключение.
9. **Какие поля нужно сохранять?** (номер, текст, дата, медиа и т.д.)
10. **Что сохраняем?** — только входящие сообщения или ещё исходящие, медиа, звонки?
11. **Нужно ли отправлять сообщения из Access обратно в WhatsApp?**
12. **Нужна ли запись в реальном времени (webhook) или можно периодически (cron)?**

---

**Пример для общения с заказчиком:**

> Чтобы сделать интеграцию WhatsApp с Access, мне нужно:
> - Номер WhatsApp, который будем подключать к API
> - Доступ к Facebook Business Manager (или добавить меня как разработчика)
> - Название компании + ИНН (для верификации Meta)
> - Где будет работать сервер (Windows/Linux, есть ли интернет, можно ли открыть порт 443)
> - Файл Access или описание нужных полей
> - Что сохраняем: только входящие сообщения или всё (включая медиа)?
> - Нужно ли отправлять сообщения из Access обратно в WhatsApp?
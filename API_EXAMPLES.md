# Примеры API запросов WhatsApp Webhook

## 🔗 Endpoints

### Локальная разработка
- Base URL: `http://localhost:5000`
- Webhook: `http://localhost:5000/webhook`

### Продакшн сервер
- Base URL: `http://*************:8080`
- Webhook: `http://*************:8080/webhook`

## 📥 Входящие запросы (от WhatsApp)

### Верификация Webhook
```bash
# GET запрос для верификации webhook
curl "http://localhost:5000/webhook?hub.verify_token=YOUR_VERIFY_TOKEN&hub.challenge=test123&hub.mode=subscribe"

# Ожидаемый ответ: test123
```

### Получение сообщения
```bash
# POST запрос с входящим сообщением
curl -X POST http://localhost:5000/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "object": "whatsapp_business_account",
    "entry": [{
      "id": "WHATSAPP_BUSINESS_ACCOUNT_ID",
      "changes": [{
        "value": {
          "messaging_product": "whatsapp",
          "metadata": {
            "display_phone_number": "***********",
            "phone_number_id": "***************"
          },
          "messages": [{
            "from": "***********",
            "id": "wamid.****************************************************",
            "timestamp": "**********",
            "text": {
              "body": "Привет! Это тестовое сообщение."
            },
            "type": "text"
          }]
        },
        "field": "messages"
      }]
    }]
  }'
```

### Статус доставки сообщения
```bash
# POST запрос со статусом доставки
curl -X POST http://localhost:5000/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "object": "whatsapp_business_account",
    "entry": [{
      "id": "WHATSAPP_BUSINESS_ACCOUNT_ID",
      "changes": [{
        "value": {
          "messaging_product": "whatsapp",
          "metadata": {
            "display_phone_number": "***********",
            "phone_number_id": "***************"
          },
          "statuses": [{
            "id": "wamid.HBgNNzk1MjI3NDI2MAA=",
            "status": "delivered",
            "timestamp": "**********",
            "recipient_id": "***********"
          }]
        },
        "field": "messages"
      }]
    }]
  }'
```

## 📤 Исходящие запросы (к WhatsApp API)

### Отправка текстового сообщения
```bash
curl -X POST \
  "https://graph.facebook.com/v18.0/YOUR_PHONE_NUMBER_ID/messages" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "messaging_product": "whatsapp",
    "to": "***********",
    "type": "text",
    "text": {
      "body": "Привет! Это сообщение отправлено через API."
    }
  }'
```

### Отправка сообщения с кнопками
```bash
curl -X POST \
  "https://graph.facebook.com/v18.0/YOUR_PHONE_NUMBER_ID/messages" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "messaging_product": "whatsapp",
    "to": "***********",
    "type": "interactive",
    "interactive": {
      "type": "button",
      "body": {
        "text": "Выберите действие:"
      },
      "action": {
        "buttons": [
          {
            "type": "reply",
            "reply": {
              "id": "btn_1",
              "title": "Да"
            }
          },
          {
            "type": "reply",
            "reply": {
              "id": "btn_2",
              "title": "Нет"
            }
          }
        ]
      }
    }
  }'
```

### Отправка изображения
```bash
curl -X POST \
  "https://graph.facebook.com/v18.0/YOUR_PHONE_NUMBER_ID/messages" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "messaging_product": "whatsapp",
    "to": "***********",
    "type": "image",
    "image": {
      "link": "https://example.com/image.jpg",
      "caption": "Описание изображения"
    }
  }'
```

### Отправка документа
```bash
curl -X POST \
  "https://graph.facebook.com/v18.0/YOUR_PHONE_NUMBER_ID/messages" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "messaging_product": "whatsapp",
    "to": "***********",
    "type": "document",
    "document": {
      "link": "https://example.com/document.pdf",
      "filename": "Документ.pdf",
      "caption": "Важный документ"
    }
  }'
```

## 🔍 Диагностические запросы

### Проверка здоровья сервера
```bash
# Проверка работоспособности
curl http://localhost:5000/health

# Ожидаемый ответ:
# {"status": "healthy", "timestamp": "2025-08-05T10:30:00Z"}
```

### Тест подключения к базе данных
```bash
# Проверка подключения к SQL Server
curl http://localhost:5000/db-test

# Успешный ответ:
# {"ok": true, "message": "Подключение к базе данных успешно"}

# Ошибка подключения:
# {"ok": false, "error": "Не удалось подключиться к базе"}
```

### Получение статистики
```bash
# Статистика сообщений
curl http://localhost:5000/stats

# Ожидаемый ответ:
# {
#   "total_messages": 150,
#   "today_messages": 25,
#   "last_message_time": "2025-08-05T10:25:00Z"
# }
```

## 🧪 Тестовые сценарии

### Полный цикл тестирования

#### 1. Проверка сервера
```bash
#!/bin/bash
echo "=== Тестирование WhatsApp Webhook ==="

# Проверка здоровья
echo "1. Проверка здоровья сервера..."
curl -s http://localhost:5000/health | jq .

# Проверка базы данных
echo "2. Проверка подключения к базе..."
curl -s http://localhost:5000/db-test | jq .

# Проверка webhook верификации
echo "3. Проверка webhook верификации..."
curl -s "http://localhost:5000/webhook?hub.verify_token=test&hub.challenge=12345&hub.mode=subscribe"
```

#### 2. Тест входящего сообщения
```bash
# Отправка тестового сообщения
curl -X POST http://localhost:5000/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "object": "whatsapp_business_account",
    "entry": [{
      "id": "TEST_ACCOUNT_ID",
      "changes": [{
        "value": {
          "messaging_product": "whatsapp",
          "metadata": {
            "display_phone_number": "***********",
            "phone_number_id": "TEST_PHONE_ID"
          },
          "messages": [{
            "from": "***********",
            "id": "test_message_id_' + $(date +%s) + '",
            "timestamp": "' + $(date +%s) + '",
            "text": {
              "body": "Тестовое сообщение от ' + $(date) + '"
            },
            "type": "text"
          }]
        },
        "field": "messages"
      }]
    }]
  }'
```

#### 3. Проверка сохранения в базе
```sql
-- Выполните в SQL Server Management Studio
SELECT TOP 10 * 
FROM whatsapp_messages 
ORDER BY created_at DESC;
```

## 📊 Мониторинг и метрики

### Проверка производительности
```bash
# Время отклика
time curl -s http://localhost:5000/health

# Нагрузочное тестирование (требует apache2-utils)
ab -n 100 -c 10 http://localhost:5000/health
```

### Мониторинг логов
```bash
# Просмотр логов в реальном времени
tail -f /var/log/whatsapp-webhook.log

# Поиск ошибок
grep -i error /var/log/whatsapp-webhook.log

# Статистика по сообщениям
grep "Получено сообщение" /var/log/whatsapp-webhook.log | wc -l
```

## 🔐 Безопасность

### Проверка токенов
```bash
# Проверка валидности Access Token
curl "https://graph.facebook.com/v18.0/me?access_token=YOUR_ACCESS_TOKEN"

# Проверка прав токена
curl "https://graph.facebook.com/v18.0/YOUR_PHONE_NUMBER_ID?access_token=YOUR_ACCESS_TOKEN"
```

### Валидация webhook подписи (для продакшена)
```python
import hmac
import hashlib

def verify_webhook_signature(payload, signature, app_secret):
    """
    Проверка подписи webhook от Facebook
    """
    expected_signature = hmac.new(
        app_secret.encode('utf-8'),
        payload.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(
        f"sha256={expected_signature}",
        signature
    )
```

## 🐛 Отладка

### Включение подробного логирования
```python
# В webhook_server.py добавьте:
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Сохранение всех запросов
```python
# Добавьте в webhook endpoint:
@app.route('/webhook', methods=['POST'])
def webhook():
    # Сохранить сырые данные для отладки
    with open('debug_requests.log', 'a') as f:
        f.write(f"{datetime.now()}: {request.get_data()}\n")
    
    # Остальная логика...
```

### Тестирование с ngrok (для локальной разработки)
```bash
# Установите ngrok
# Запустите туннель
ngrok http 5000

# Используйте URL от ngrok в настройках Facebook webhook
# Например: https://abc123.ngrok.io/webhook
```

## 📝 Примеры ответов API

### Успешная отправка сообщения
```json
{
  "messaging_product": "whatsapp",
  "contacts": [{
    "input": "***********",
    "wa_id": "***********"
  }],
  "messages": [{
    "id": "wamid.HBgNNzk1MjI3NDI2MAA="
  }]
}
```

### Ошибка отправки
```json
{
  "error": {
    "message": "Invalid parameter",
    "type": "OAuthException",
    "code": 100,
    "error_subcode": 2388001,
    "fbtrace_id": "A1B2C3D4E5F6G7H8"
  }
}
```

### Входящее сообщение
```json
{
  "object": "whatsapp_business_account",
  "entry": [{
    "id": "WHATSAPP_BUSINESS_ACCOUNT_ID",
    "changes": [{
      "value": {
        "messaging_product": "whatsapp",
        "metadata": {
          "display_phone_number": "***********",
          "phone_number_id": "***************"
        },
        "messages": [{
          "from": "***********",
          "id": "wamid.HBgNNzk1MjI3NDI2MAA=",
          "timestamp": "**********",
          "text": {
            "body": "Привет!"
          },
          "type": "text"
        }]
      },
      "field": "messages"
    }]
  }]
}
```

---

**Версия документации**: 1.0  
**Дата обновления**: 2025-08-05  
**Автор**: WhatsApp Integration Team
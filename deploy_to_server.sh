#!/bin/bash
# Скрипт развёртывания WhatsApp Webhook Server на Ubuntu/CentOS

set -e  # Остановка при ошибке

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Функция для логирования
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ОШИБКА] $1${NC}" >&2
}

warning() {
    echo -e "${YELLOW}[ПРЕДУПРЕЖДЕНИЕ] $1${NC}"
}

# Проверка прав root
if [[ $EUID -ne 0 ]]; then
   error "Этот скрипт должен запускаться с правами root"
   exit 1
fi

log "=== Развёртывание WhatsApp Webhook Server ==="

# Определение дистрибутива
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VER=$VERSION_ID
else
    error "Не удается определить операционную систему"
    exit 1
fi

log "Операционная система: $OS $VER"

# Обновление системы
log "Обновление пакетов..."
if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
    apt update && apt upgrade -y
elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
    yum update -y
else
    warning "Неизвестная ОС, пропускаю обновление пакетов"
fi

# Установка Python и зависимостей
log "Установка Python и зависимостей..."
if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
    apt install -y python3 python3-pip python3-venv nginx ufw
elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
    yum install -y python3 python3-pip nginx firewalld
    systemctl enable firewalld
    systemctl start firewalld
else
    error "Неподдерживаемая операционная система: $OS"
    exit 1
fi

# Создание пользователя для приложения
log "Создание пользователя whatsapp..."
if ! id "whatsapp" &>/dev/null; then
    useradd -m -s /bin/bash whatsapp
    log "Пользователь whatsapp создан"
else
    log "Пользователь whatsapp уже существует"
fi

# Создание директории приложения
APP_DIR="/opt/whatsapp-webhook"
log "Создание директории $APP_DIR..."
mkdir -p $APP_DIR
chown whatsapp:whatsapp $APP_DIR

# Копирование файлов из временной директории
TEMP_DIR="/tmp/whatsapp-integration"
if [ -d "$TEMP_DIR" ]; then
    log "Копирование файлов из $TEMP_DIR..."
    cp -r $TEMP_DIR/* $APP_DIR/
    chown -R whatsapp:whatsapp $APP_DIR
else
    warning "Директория $TEMP_DIR не найдена. Файлы нужно будет скопировать вручную."
fi

# Переход в директорию приложения
cd $APP_DIR

# Создание виртуального окружения
log "Создание виртуального окружения..."
sudo -u whatsapp python3 -m venv venv
sudo -u whatsapp ./venv/bin/pip install --upgrade pip

# Установка зависимостей
if [ -f "requirements.txt" ]; then
    log "Установка зависимостей из requirements.txt..."
    sudo -u whatsapp ./venv/bin/pip install -r requirements.txt
else
    log "Установка базовых зависимостей..."
    sudo -u whatsapp ./venv/bin/pip install flask python-dotenv pyodbc requests gunicorn
fi

# Проверка наличия основного файла
if [ ! -f "webhook_server.py" ]; then
    error "Файл webhook_server.py не найден в $APP_DIR"
    error "Убедитесь, что файлы были загружены правильно"
    exit 1
fi

# Создание systemd службы
log "Создание systemd службы..."
cat > /etc/systemd/system/whatsapp-webhook.service << 'EOF'
[Unit]
Description=WhatsApp Webhook Server
After=network.target

[Service]
Type=exec
User=whatsapp
Group=whatsapp
WorkingDirectory=/opt/whatsapp-webhook
Environment=PATH=/opt/whatsapp-webhook/venv/bin
ExecStart=/opt/whatsapp-webhook/venv/bin/gunicorn -w 4 -b 127.0.0.1:5000 webhook_server:app
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

# Настройка Nginx
log "Настройка Nginx..."
if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
    cat > /etc/nginx/sites-available/whatsapp-webhook << 'EOF'
server {
    listen 80;
    server_name _;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF
    # Активация сайта Nginx
    ln -sf /etc/nginx/sites-available/whatsapp-webhook /etc/nginx/sites-enabled/
    rm -f /etc/nginx/sites-enabled/default
elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
    cat > /etc/nginx/conf.d/whatsapp-webhook.conf << 'EOF'
server {
    listen 80;
    server_name _;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF
fi

# Настройка firewall
log "Настройка firewall..."
if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
    ufw allow ssh
    ufw allow 'Nginx Full'
    ufw --force enable
elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
    firewall-cmd --permanent --add-service=ssh
    firewall-cmd --permanent --add-service=http
    firewall-cmd --permanent --add-service=https
    firewall-cmd --reload
fi

# Создание директории для логов
log "Создание директории для логов..."
mkdir -p /var/log/whatsapp-webhook
chown whatsapp:whatsapp /var/log/whatsapp-webhook

# Настройка .env файла
log "Настройка файла конфигурации..."
if [ -f ".env" ]; then
    chmod 600 .env
    chown whatsapp:whatsapp .env
    log "Файл .env настроен. Не забудьте отредактировать его!"
else
    warning "Файл .env не найден. Создайте его из config.env.example"
fi

# Перезагрузка служб
log "Перезагрузка служб..."
systemctl daemon-reload
systemctl enable whatsapp-webhook
systemctl enable nginx

# Проверка конфигурации Nginx
if nginx -t; then
    systemctl restart nginx
    log "Nginx перезапущен успешно"
else
    error "Ошибка в конфигурации Nginx"
    exit 1
fi

# Финальные проверки
log "Выполнение финальных проверок..."

# Проверка статуса служб
if systemctl is-enabled nginx >/dev/null; then
    log "✓ Nginx включен в автозагрузку"
else
    warning "✗ Nginx не включен в автозагрузку"
fi

if systemctl is-enabled whatsapp-webhook >/dev/null; then
    log "✓ WhatsApp Webhook включен в автозагрузку"
else
    warning "✗ WhatsApp Webhook не включен в автозагрузку"
fi

log "=== Развёртывание завершено успешно! ==="
echo
log "Следующие шаги:"
echo "1. Отредактируйте файл конфигурации: nano $APP_DIR/.env"
echo "2. Запустите службу: systemctl start whatsapp-webhook"
echo "3. Проверьте статус: systemctl status whatsapp-webhook"
echo "4. Просмотрите логи: journalctl -u whatsapp-webhook -f"
echo "5. Проверьте работу: curl http://localhost/health"
echo
log "Полезные команды:"
echo "• Перезапуск службы: systemctl restart whatsapp-webhook"
echo "• Просмотр логов: tail -f /var/log/whatsapp-webhook/webhook.log"
echo "• Проверка Nginx: nginx -t"
echo "• Статус всех служб: systemctl status nginx whatsapp-webhook"
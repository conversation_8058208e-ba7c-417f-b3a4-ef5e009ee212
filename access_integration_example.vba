' =====================================================
' WhatsApp Integration VBA Code for Microsoft Access
' =====================================================

' Модуль для работы с WhatsApp сообщениями через SQLite базу данных
' Требует подключения к SQLite через ODBC драйвер

Option Compare Database
Option Explicit

' Константы
Private Const DB_PATH As String = "whatsapp_test.db"
Private Const WEBHOOK_URL As String = "http://localhost:5000"

' =====================================================
' Функция для чтения новых сообщений из SQLite
' =====================================================
Public Sub ReadNewMessages()
    On Error GoTo ErrorHandler
    
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim sql As String
    
    ' SQL запрос для получения непрочитанных сообщений
    sql = "SELECT MessageID, FromUser, Text, SentAt, MessageType " & _
          "FROM Messages " & _
          "WHERE Status = 'received' " & _
          "ORDER BY SentAt DESC"
    
    ' Подключение к SQLite через ODBC
    Set db = OpenDatabase(DB_PATH, False, False, "ODBC;DRIVER=SQLite3 ODBC Driver;DATABASE=" & DB_PATH)
    Set rs = db.OpenRecordset(sql)
    
    ' Обработка каждого сообщения
    Do While Not rs.EOF
        ' Вывод информации о сообщении
        Debug.Print "Новое сообщение от: " & rs!FromUser
        Debug.Print "Текст: " & rs!Text
        Debug.Print "Время: " & rs!SentAt
        Debug.Print "Тип: " & rs!MessageType
        Debug.Print "---"
        
        ' Здесь можно добавить логику обработки сообщения
        ' Например, сохранение в таблицу Access или отправка уведомления
        
        rs.MoveNext
    Loop
    
    rs.Close
    db.Close
    
    MsgBox "Обработка сообщений завершена", vbInformation
    Exit Sub
    
ErrorHandler:
    MsgBox "Ошибка при чтении сообщений: " & Err.Description, vbCritical
    If Not rs Is Nothing Then rs.Close
    If Not db Is Nothing Then db.Close
End Sub

' =====================================================
' Функция для отправки сообщения через API
' =====================================================
Public Function SendWhatsAppMessage(phoneNumber As String, messageText As String) As Boolean
    On Error GoTo ErrorHandler
    
    Dim http As Object
    Dim jsonData As String
    Dim response As String
    
    ' Создание HTTP объекта
    Set http = CreateObject("MSXML2.XMLHTTP")
    
    ' Формирование JSON данных
    jsonData = "{""to"":""" & phoneNumber & """,""message"":""" & messageText & """}"
    
    ' Отправка POST запроса
    http.Open "POST", WEBHOOK_URL & "/send", False
    http.setRequestHeader "Content-Type", "application/json"
    http.send jsonData
    
    ' Проверка ответа
    If http.Status = 200 Then
        response = http.responseText
        Debug.Print "Сообщение отправлено: " & response
        SendWhatsAppMessage = True
    Else
        Debug.Print "Ошибка отправки: " & http.Status & " - " & http.statusText
        SendWhatsAppMessage = False
    End If
    
    Set http = Nothing
    Exit Function
    
ErrorHandler:
    MsgBox "Ошибка при отправке сообщения: " & Err.Description, vbCritical
    SendWhatsAppMessage = False
    Set http = Nothing
End Function

' =====================================================
' Функция для получения статистики контактов
' =====================================================
Public Sub GetContactStats()
    On Error GoTo ErrorHandler
    
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim sql As String
    
    sql = "SELECT PhoneNumber, ContactName, MessageCount, LastMessageAt " & _
          "FROM Contacts " & _
          "WHERE IsActive = 1 " & _
          "ORDER BY MessageCount DESC"
    
    Set db = OpenDatabase(DB_PATH, False, False, "ODBC;DRIVER=SQLite3 ODBC Driver;DATABASE=" & DB_PATH)
    Set rs = db.OpenRecordset(sql)
    
    Debug.Print "=== СТАТИСТИКА КОНТАКТОВ ==="
    
    Do While Not rs.EOF
        Debug.Print "Телефон: " & rs!PhoneNumber
        Debug.Print "Имя: " & Nz(rs!ContactName, "Не указано")
        Debug.Print "Сообщений: " & rs!MessageCount
        Debug.Print "Последнее сообщение: " & Nz(rs!LastMessageAt, "Никогда")
        Debug.Print "---"
        
        rs.MoveNext
    Loop
    
    rs.Close
    db.Close
    Exit Sub
    
ErrorHandler:
    MsgBox "Ошибка при получении статистики: " & Err.Description, vbCritical
    If Not rs Is Nothing Then rs.Close
    If Not db Is Nothing Then db.Close
End Sub

' =====================================================
' Функция для проверки состояния сервера
' =====================================================
Public Function CheckServerHealth() As Boolean
    On Error GoTo ErrorHandler
    
    Dim http As Object
    Dim response As String
    
    Set http = CreateObject("MSXML2.XMLHTTP")
    
    http.Open "GET", WEBHOOK_URL & "/health", False
    http.send
    
    If http.Status = 200 Then
        response = http.responseText
        Debug.Print "Статус сервера: " & response
        
        ' Проверяем, содержит ли ответ "healthy"
        If InStr(response, "healthy") > 0 Then
            CheckServerHealth = True
            MsgBox "Сервер работает нормально", vbInformation
        Else
            CheckServerHealth = False
            MsgBox "Сервер работает, но есть проблемы: " & response, vbWarning
        End If
    Else
        CheckServerHealth = False
        MsgBox "Сервер недоступен: " & http.Status & " - " & http.statusText, vbCritical
    End If
    
    Set http = Nothing
    Exit Function
    
ErrorHandler:
    MsgBox "Ошибка при проверке сервера: " & Err.Description, vbCritical
    CheckServerHealth = False
    Set http = Nothing
End Function

' =====================================================
' Функция для автоматической обработки сообщений
' Можно вызывать по таймеру
' =====================================================
Public Sub AutoProcessMessages()
    On Error GoTo ErrorHandler
    
    ' Проверяем состояние сервера
    If Not CheckServerHealth() Then
        Debug.Print "Сервер недоступен, пропускаем обработку"
        Exit Sub
    End If
    
    ' Читаем новые сообщения
    ReadNewMessages
    
    ' Здесь можно добавить дополнительную логику:
    ' - Автоответы
    ' - Уведомления
    ' - Интеграция с CRM
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Ошибка автообработки: " & Err.Description, vbCritical
End Sub

' =====================================================
' Пример использования в форме Access
' =====================================================

' Обработчик кнопки "Отправить сообщение"
Private Sub btnSendMessage_Click()
    Dim phone As String
    Dim message As String
    
    phone = Me.txtPhoneNumber.Value
    message = Me.txtMessage.Value
    
    If Len(phone) = 0 Or Len(message) = 0 Then
        MsgBox "Заполните номер телефона и текст сообщения", vbWarning
        Exit Sub
    End If
    
    If SendWhatsAppMessage(phone, message) Then
        MsgBox "Сообщение отправлено успешно!", vbInformation
        Me.txtMessage.Value = "" ' Очищаем поле сообщения
    Else
        MsgBox "Ошибка отправки сообщения", vbCritical
    End If
End Sub

' Обработчик кнопки "Обновить сообщения"
Private Sub btnRefreshMessages_Click()
    AutoProcessMessages
End Sub

' Обработчик кнопки "Статистика"
Private Sub btnShowStats_Click()
    GetContactStats
End Sub

' =====================================================
' ИНСТРУКЦИИ ПО НАСТРОЙКЕ:
' =====================================================
'
' 1. Установите SQLite ODBC драйвер:
'    http://www.ch-werner.de/sqliteodbc/
'
' 2. Создайте форму в Access с полями:
'    - txtPhoneNumber (текстовое поле для номера)
'    - txtMessage (текстовое поле для сообщения)
'    - btnSendMessage (кнопка отправки)
'    - btnRefreshMessages (кнопка обновления)
'    - btnShowStats (кнопка статистики)
'
' 3. Скопируйте этот код в модуль формы
'
' 4. Убедитесь, что WhatsApp сервер запущен на localhost:5000
'
' 5. Путь к базе данных должен быть правильным (DB_PATH)
'
' =====================================================
# Структура таблиц для интеграции WhatsApp с Microsoft Access

## 📋 Обзор таблиц

Система интеграции WhatsApp с Access состоит из 5 основных таблиц:

1. **Messages** - входящие сообщения WhatsApp
2. **OutgoingMessages** - исходящие сообщения для отправки
3. **Contacts** - информация о контактах
4. **Settings** - настройки интеграции
5. **Logs** - логирование операций

---

## 📨 Messages (Входящие сообщения)

Основная таблица для хранения всех входящих сообщений от WhatsApp.

| Поле | Тип | Описание | Обязательное |
|------|-----|----------|--------------|
| **MessageID** | VARCHAR(255) | Уникальный ID сообщения от WhatsApp | ✅ PRIMARY KEY |
| **ChatID** | VARCHAR(255) | ID чата/диалога | ✅ |
| **FromUser** | VARCHAR(255) | Номер отправителя (без кода страны) | ✅ |
| **ToUser** | VARCHAR(255) | Номер получателя (ваш номер) | ✅ |
| **Text** | TEXT | Текст сообщения | ❌ |
| **MessageType** | VARCHAR(50) | Тип сообщения (text, image, document, etc.) | ❌ |
| **MediaURL** | VARCHAR(500) | URL медиафайла (если есть) | ❌ |
| **SentAt** | DATETIME | Время отправки сообщения | ✅ |
| **ReceivedAt** | DATETIME | Время получения на сервере | ❌ (DEFAULT GETDATE()) |
| **Status** | VARCHAR(50) | Статус обработки | ❌ (DEFAULT 'received') |
| **RawData** | TEXT | Полные данные от WhatsApp API | ❌ |

### Индексы:
- `IX_Messages_ChatID` - для быстрого поиска по чату
- `IX_Messages_FromUser` - для поиска сообщений от пользователя
- `IX_Messages_SentAt` - для сортировки по времени
- `IX_Messages_Status` - для фильтрации по статусу

---

## 📤 OutgoingMessages (Исходящие сообщения)

Таблица для отправки сообщений из Access в WhatsApp.

| Поле | Тип | Описание | Обязательное |
|------|-----|----------|--------------|
| **ID** | INT | Автоинкрементный ID | ✅ PRIMARY KEY (IDENTITY) |
| **ToUser** | VARCHAR(255) | Номер получателя | ✅ |
| **MessageText** | TEXT | Текст сообщения | ✅ |
| **MessageType** | VARCHAR(50) | Тип сообщения | ❌ (DEFAULT 'text') |
| **MediaPath** | VARCHAR(500) | Путь к медиафайлу (если есть) | ❌ |
| **Priority** | INT | Приоритет отправки | ❌ (DEFAULT 1) |
| **ScheduledAt** | DATETIME | Время запланированной отправки | ❌ |
| **CreatedAt** | DATETIME | Время создания записи | ❌ (DEFAULT GETDATE()) |
| **Status** | VARCHAR(50) | Статус: pending/sent/failed | ❌ (DEFAULT 'pending') |
| **WhatsAppMessageID** | VARCHAR(255) | ID сообщения в WhatsApp (после отправки) | ❌ |
| **ErrorMessage** | TEXT | Сообщение об ошибке (если есть) | ❌ |

### Статусы:
- `pending` - ожидает отправки
- `sent` - отправлено успешно
- `failed` - ошибка отправки

### Индексы:
- `IX_OutgoingMessages_Status` - для поиска по статусу
- `IX_OutgoingMessages_ScheduledAt` - для планировщика
- `IX_OutgoingMessages_ToUser` - для поиска по получателю

---

## 👥 Contacts (Контакты)

Таблица для хранения информации о контактах.

| Поле | Тип | Описание | Обязательное |
|------|-----|----------|--------------|
| **PhoneNumber** | VARCHAR(255) | Номер телефона (без кода страны) | ✅ PRIMARY KEY |
| **ContactName** | VARCHAR(255) | Имя контакта | ❌ |
| **Company** | VARCHAR(255) | Компания | ❌ |
| **Email** | VARCHAR(255) | Email | ❌ |
| **Notes** | TEXT | Заметки | ❌ |
| **CreatedAt** | DATETIME | Дата создания записи | ❌ (DEFAULT GETDATE()) |
| **LastMessageAt** | DATETIME | Время последнего сообщения | ❌ |
| **MessageCount** | INT | Количество сообщений | ❌ (DEFAULT 0) |
| **IsActive** | BIT | Активен ли контакт | ❌ (DEFAULT 1) |

### Индексы:
- `IX_Contacts_LastMessageAt` - для сортировки по активности
- `IX_Contacts_IsActive` - для фильтрации активных контактов

---

## ⚙️ Settings (Настройки)

Таблица для хранения настроек интеграции.

| Поле | Тип | Описание | Обязательное |
|------|-----|----------|--------------|
| **SettingKey** | VARCHAR(100) | Ключ настройки | ✅ PRIMARY KEY |
| **SettingValue** | TEXT | Значение настройки | ✅ |
| **Description** | VARCHAR(255) | Описание | ❌ |
| **UpdatedAt** | DATETIME | Время обновления | ❌ (DEFAULT GETDATE()) |

### Базовые настройки:
- `whatsapp_phone_number_id` - Phone Number ID из Meta
- `whatsapp_business_account_id` - Business Account ID из Meta
- `whatsapp_access_token` - Access Token для API
- `webhook_verify_token` - Токен для верификации webhook
- `auto_reply_enabled` - Включить автоответы (0/1)
- `auto_reply_message` - Текст автоответа
- `max_message_length` - Максимальная длина сообщения

---

## 📝 Logs (Логи)

Таблица для логирования операций системы.

| Поле | Тип | Описание | Обязательное |
|------|-----|----------|--------------|
| **ID** | INT | Автоинкрементный ID | ✅ PRIMARY KEY (IDENTITY) |
| **LogLevel** | VARCHAR(20) | Уровень лога (INFO/WARNING/ERROR) | ✅ |
| **Message** | TEXT | Текст сообщения | ✅ |
| **Source** | VARCHAR(100) | Источник (webhook, sender, etc.) | ❌ |
| **CreatedAt** | DATETIME | Время создания записи | ❌ (DEFAULT GETDATE()) |

### Уровни логов:
- `INFO` - информационные сообщения
- `WARNING` - предупреждения
- `ERROR` - ошибки

### Индексы:
- `IX_Logs_CreatedAt` - для сортировки по времени
- `IX_Logs_LogLevel` - для фильтрации по уровню

---

## 🔍 Представления (Views)

### vw_RecentMessages
Показывает последние сообщения с именами контактов.

**Поля:**
- MessageID, ChatID, FromUser, ContactName, Text, MessageType, SentAt, Status

### vw_UnreadMessages
Показывает непрочитанные сообщения.

**Поля:**
- MessageID, ChatID, FromUser, ContactName, Text, SentAt

**Условие:** Status = 'received'

### vw_ContactStats
Показывает статистику по контактам.

**Поля:**
- PhoneNumber, ContactName, Company, MessageCount, LastMessageAt, IsActive

---

## 🔗 Связи между таблицами

```
Messages.FromUser → Contacts.PhoneNumber
OutgoingMessages.ToUser → Contacts.PhoneNumber
```

---

## 📊 Примеры запросов

### Последние сообщения с именами контактов:
```sql
SELECT * FROM vw_RecentMessages;
```

### Непрочитанные сообщения:
```sql
SELECT * FROM vw_UnreadMessages;
```

### Статистика по контактам:
```sql
SELECT * FROM vw_ContactStats;
```

### Сообщения конкретного пользователя:
```sql
SELECT * FROM Messages 
WHERE FromUser = '79001234567' 
ORDER BY SentAt DESC;
```

### Исходящие сообщения в статусе pending:
```sql
SELECT * FROM OutgoingMessages 
WHERE Status = 'pending' 
ORDER BY CreatedAt;
```

### Логи ошибок:
```sql
SELECT * FROM Logs 
WHERE LogLevel = 'ERROR' 
ORDER BY CreatedAt DESC;
```

---

## 🚀 Рекомендации по использованию

1. **Индексы** - все необходимые индексы созданы автоматически
2. **Статусы** - используйте стандартные статусы для отслеживания состояния
3. **Логирование** - ведите логи всех операций для отладки
4. **Настройки** - храните все параметры интеграции в таблице Settings
5. **Контакты** - автоматически обновляйте статистику при получении сообщений

---

## 📋 Чек-лист установки

- [ ] Выполнить основной SQL-скрипт
- [ ] Проверить создание всех таблиц
- [ ] Проверить создание индексов
- [ ] Проверить создание представлений
- [ ] Заполнить настройки в таблице Settings
- [ ] Добавить тестовые данные (опционально)
- [ ] Проверить работу представлений 
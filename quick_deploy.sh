#!/bin/bash
# Быстрое развертывание WhatsApp интеграции на сервер
# Использование: ./quick_deploy.sh [IP_СЕРВЕРА] [ПОЛЬЗОВАТЕЛЬ]

set -e

# Параметры
SERVER_IP="${1:-*************}"
SERVER_USER="${2:-root}"
SERVER_PATH="/tmp/whatsapp-integration"

# Цвета
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ОШИБКА] $1${NC}" >&2
}

warning() {
    echo -e "${YELLOW}[ВНИМАНИЕ] $1${NC}"
}

echo "========================================="
echo "  БЫСТРОЕ РАЗВЕРТЫВАНИЕ WHATSAPP API"
echo "========================================="
echo "Сервер: $SERVER_USER@$SERVER_IP"
echo "========================================="

# Проверка подключения
log "Проверка подключения к серверу..."
if ! ssh -o ConnectTimeout=5 -o BatchMode=yes "$SERVER_USER@$SERVER_IP" exit 2>/dev/null; then
    error "Не удается подключиться к серверу!"
    echo "Попробуйте:"
    echo "ssh $SERVER_USER@$SERVER_IP"
    exit 1
fi

# Создание директории
log "Создание директории на сервере..."
ssh "$SERVER_USER@$SERVER_IP" "rm -rf $SERVER_PATH && mkdir -p $SERVER_PATH"

# Загрузка всех файлов
log "Загрузка файлов..."
scp -r \
    webhook_server.py \
    requirements.txt \
    config.env.example \
    deploy_to_server.sh \
    upload_to_server.sh \
    whatsapp_access_tables.sql \
    access_integration.vba \
    table_structure.md \
    README.md \
    sample_data.sql \
    "$SERVER_USER@$SERVER_IP:$SERVER_PATH/" 2>/dev/null || warning "Некоторые файлы не найдены"

# Настройка прав и создание .env
log "Настройка файлов на сервере..."
ssh "$SERVER_USER@$SERVER_IP" << 'ENDSSH'
cd /tmp/whatsapp-integration
chmod +x deploy_to_server.sh upload_to_server.sh
cp config.env.example .env
echo "# Отредактируйте эти настройки перед запуском!" >> .env
echo "ACCESS_DSN=MS Access Database" >> .env
echo "WEBHOOK_VERIFY_TOKEN=your_verify_token_here" >> .env
echo "WHATSAPP_APP_SECRET=your_app_secret_here" >> .env
echo "PORT=5000" >> .env
echo "DEBUG=False" >> .env
ENDSSH

log "Файлы загружены успешно!"
echo
echo "========================================="
echo "  СЛЕДУЮЩИЕ ШАГИ:"
echo "========================================="
echo "1. Подключитесь к серверу:"
echo "   ssh $SERVER_USER@$SERVER_IP"
echo
echo "2. Перейдите в директорию:"
echo "   cd $SERVER_PATH"
echo
echo "3. Отредактируйте конфигурацию:"
echo "   nano .env"
echo
echo "4. Запустите развертывание:"
echo "   ./deploy_to_server.sh"
echo
echo "========================================="
echo "  АВТОМАТИЧЕСКОЕ РАЗВЕРТЫВАНИЕ:"
echo "========================================="
read -p "Запустить развертывание автоматически? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log "Запуск автоматического развертывания..."
    ssh "$SERVER_USER@$SERVER_IP" "cd $SERVER_PATH && ./deploy_to_server.sh"
    
    echo
    log "Развертывание завершено!"
    echo "Проверьте работу: curl http://$SERVER_IP/health"
else
    log "Файлы готовы к развертыванию на сервере"
fi
# 📚 Документация WhatsApp Webhook

Полная документация по развертыванию, настройке и использованию WhatsApp Webhook сервера.

## 📋 Обзор документации

### 🚀 Быстрый старт
- **[QUICK_START.md](QUICK_START.md)** - Краткое руководство по быстрому запуску
  - Локальная разработка за 5 минут
  - Развертывание на сервере
  - Чек-листы настройки
  - Быстрая диагностика проблем

### 🔧 Подробное развертывание
- **[DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md)** - Полное руководство по развертыванию
  - Требования к системе
  - Пошаговая установка
  - Настройка окружения
  - Мониторинг и обслуживание

### 🔌 Подключения
- **[CONNECTION_GUIDE.md](CONNECTION_GUIDE.md)** - Руководство по подключениям
  - SQL Server подключение
  - WhatsApp Business API
  - Сетевые настройки
  - Тестирование подключений

### 🛠️ Конфигурации
- **[CONFIG_TEMPLATES.md](CONFIG_TEMPLATES.md)** - Шаблоны конфигураций
  - Файлы окружения (.env)
  - Supervisor, Nginx, Systemd
  - Скрипты развертывания
  - Настройки безопасности

### 📡 API и примеры
- **[API_EXAMPLES.md](API_EXAMPLES.md)** - Примеры API запросов
  - Входящие webhook запросы
  - Исходящие API вызовы
  - Тестовые сценарии
  - Отладка и мониторинг

## 🏗️ Архитектура системы

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   WhatsApp      │    │   Webhook       │    │   SQL Server    │
│   Business API  │◄──►│   Server        │◄──►│   Database      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Логирование   │
                       │   и мониторинг  │
                       └─────────────────┘
```

## 🎯 Основные компоненты

### 1. Webhook Server (`webhook_server.py`)
- Принимает входящие сообщения от WhatsApp
- Обрабатывает различные типы сообщений
- Сохраняет данные в SQL Server
- Предоставляет API для диагностики

### 2. База данных (SQL Server)
- Хранение сообщений и метаданных
- Логирование событий
- Статистика и аналитика

### 3. Конфигурация
- Переменные окружения (.env)
- Настройки подключений
- Параметры безопасности

## 🚦 Статусы развертывания

### ✅ Готово к использованию
- [x] Базовый webhook сервер
- [x] Подключение к SQL Server
- [x] Обработка текстовых сообщений
- [x] Логирование событий
- [x] Диагностические endpoints

### 🔄 В разработке
- [ ] Обработка медиа файлов
- [ ] Интерактивные сообщения
- [ ] Массовая рассылка
- [ ] Web интерфейс администратора

### 📋 Планируется
- [ ] Интеграция с CRM
- [ ] Автоматические ответы
- [ ] Аналитика и отчеты
- [ ] Многоязычная поддержка

## 🔧 Быстрая настройка

### Для разработчиков
```bash
# 1. Клонирование проекта
git clone <repository-url>
cd whatsapp-webhook

# 2. Создание виртуального окружения
python -m venv venv
source venv/bin/activate  # Linux/Mac
# или
venv\Scripts\activate     # Windows

# 3. Установка зависимостей
pip install -r requirements.txt

# 4. Настройка окружения
cp .env.example .env
# Отредактируйте .env файл

# 5. Запуск сервера
python webhook_server.py
```

### Для системных администраторов
```bash
# 1. Развертывание на сервере
sudo ./scripts/deploy.sh

# 2. Настройка автозапуска
sudo systemctl enable whatsapp-webhook

# 3. Настройка Nginx (опционально)
sudo cp config/nginx.conf /etc/nginx/sites-available/whatsapp-webhook
sudo ln -s /etc/nginx/sites-available/whatsapp-webhook /etc/nginx/sites-enabled/
sudo systemctl reload nginx

# 4. Проверка работоспособности
curl http://localhost:8080/health
```

## 🔍 Диагностика проблем

### Частые проблемы и решения

| Проблема | Симптом | Решение |
|----------|---------|---------|
| Ошибка подключения к БД | `Login timeout expired` | Проверить настройки SQL Server, файрвол |
| Webhook не получает сообщения | Нет логов входящих сообщений | Проверить URL webhook в Facebook |
| Сервер не запускается | `Port already in use` | Изменить порт или остановить другой процесс |
| Ошибки аутентификации | `Login failed for user` | Проверить учетные данные SQL Server |

### Команды диагностики
```bash
# Проверка статуса сервиса
sudo systemctl status whatsapp-webhook

# Просмотр логов
sudo journalctl -u whatsapp-webhook -f

# Тест подключения к базе
curl http://localhost:8080/db-test

# Проверка webhook
curl "http://localhost:8080/webhook?hub.verify_token=test&hub.challenge=12345&hub.mode=subscribe"
```

## 📊 Мониторинг

### Ключевые метрики
- **Доступность сервиса**: `/health` endpoint
- **Подключение к БД**: `/db-test` endpoint
- **Количество сообщений**: Логи и база данных
- **Время отклика**: Мониторинг производительности

### Логи
- **Приложение**: `/var/log/whatsapp-webhook.log`
- **Система**: `journalctl -u whatsapp-webhook`
- **Nginx**: `/var/log/nginx/whatsapp-webhook-*.log`

## 🔐 Безопасность

### Рекомендации
1. **Используйте HTTPS** для продакшн окружения
2. **Ограничьте доступ** к серверу по IP
3. **Регулярно обновляйте** зависимости
4. **Мониторьте логи** на предмет подозрительной активности
5. **Создавайте резервные копии** базы данных

### Проверка безопасности
```bash
# Проверка прав доступа к файлам
ls -la .env*

# Проверка открытых портов
sudo netstat -tlnp | grep :8080

# Проверка процессов
ps aux | grep webhook
```

## 📞 Поддержка

### Контакты
- **Техническая поддержка**: [<EMAIL>](mailto:<EMAIL>)
- **Документация**: [docs.yourdomain.com](https://docs.yourdomain.com)
- **Баг-трекер**: [github.com/yourorg/whatsapp-webhook/issues](https://github.com/yourorg/whatsapp-webhook/issues)

### Полезные ссылки
- [WhatsApp Business API Documentation](https://developers.facebook.com/docs/whatsapp)
- [SQL Server Documentation](https://docs.microsoft.com/en-us/sql/sql-server/)
- [Python Flask Documentation](https://flask.palletsprojects.com/)

## 📝 Changelog

### v1.0.0 (2025-08-05)
- ✅ Базовая функциональность webhook
- ✅ Подключение к SQL Server
- ✅ Обработка текстовых сообщений
- ✅ Логирование и диагностика
- ✅ Полная документация

### Планируемые обновления
- **v1.1.0**: Обработка медиа файлов
- **v1.2.0**: Web интерфейс администратора
- **v2.0.0**: Интеграция с CRM системами

## 📄 Лицензия

Этот проект распространяется под лицензией MIT. См. файл [LICENSE](LICENSE) для подробностей.

---

**Версия документации**: 1.0  
**Дата обновления**: 2025-08-05  
**Автор**: WhatsApp Integration Team

---

## 🎯 Следующие шаги

1. **Начните с** [QUICK_START.md](QUICK_START.md) для быстрого запуска
2. **Изучите** [DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md) для полного понимания
3. **Настройте подключения** по [CONNECTION_GUIDE.md](CONNECTION_GUIDE.md)
4. **Используйте шаблоны** из [CONFIG_TEMPLATES.md](CONFIG_TEMPLATES.md)
5. **Тестируйте API** с помощью [API_EXAMPLES.md](API_EXAMPLES.md)

**Удачного развертывания! 🚀**
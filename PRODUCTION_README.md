# 🚀 Продакшн-развертывание WhatsApp Access Integration

## 📋 Обзор системы

Полная интеграция WhatsApp Cloud API с Microsoft Access, включающая:

- **Webhook сервер** на Python Flask
- **VBA модуль** для работы с Access
- **SQL таблицы** для хранения данных
- **Автоматическая обработка** входящих сообщений
- **API для отправки** сообщений из Access

---

## 🛠️ Требования к системе

### Минимальные требования:
- **Windows 10/11** или **Windows Server 2016+**
- **Python 3.8+**
- **Microsoft Access 2016+** или **SQL Server**
- **ODBC драйвер** для Access/SQL Server
- **Интернет-соединение** для webhook'ов

### Рекомендуемые требования:
- **Windows Server 2019/2022**
- **Python 3.10+**
- **SQL Server Express/Standard**
- **SSL сертификат** для HTTPS
- **Статический IP** или домен

---

## 📦 Установка и настройка

### 1. Подготовка системы

```bash
# Клонирование/скачивание проекта
git clone <repository-url>
cd whatsapp-access-integration

# Или распаковка архива
# whatsapp_access_integration_tables.zip
```

### 2. Установка зависимостей

```bash
# Установка Python зависимостей
pip install -r requirements.txt

# Или через batch файл
deploy.bat
```

### 3. Настройка базы данных

1. **Создание таблиц:**
   ```sql
   -- Выполнить скрипт в SQL Server Management Studio
   whatsapp_access_tables.sql
   ```

2. **Настройка ODBC DSN:**
   - Открыть "Источники данных ODBC"
   - Создать новый DSN для вашей базы
   - Запомнить имя DSN

### 4. Настройка конфигурации

```bash
# Копирование примера конфигурации
copy config.env.example .env

# Редактирование .env файла
notepad .env
```

**Обязательные настройки в .env:**
```env
# База данных
ACCESS_DSN=Your_Access_DSN_Name

# WhatsApp API (получить в Meta Developers)
WHATSAPP_APP_SECRET=your_app_secret_here
WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token

# Сервер
PORT=5000
DEBUG=False
```

### 5. Настройка WhatsApp API

1. **Регистрация в Meta Developers:**
   - Создать приложение на [developers.facebook.com](https://developers.facebook.com)
   - Добавить продукт WhatsApp Business API
   - Получить Phone Number ID и Access Token

2. **Настройка webhook:**
   - URL: `https://your-domain.com/webhook`
   - Verify Token: значение из .env файла
   - Подписать на события: `messages`

3. **Заполнение настроек в базе:**
   ```sql
   UPDATE Settings SET SettingValue = 'your_phone_number_id' WHERE SettingKey = 'whatsapp_phone_number_id';
   UPDATE Settings SET SettingValue = 'your_access_token' WHERE SettingKey = 'whatsapp_access_token';
   UPDATE Settings SET SettingValue = 'your_webhook_verify_token' WHERE SettingKey = 'webhook_verify_token';
   ```

---

## 🚀 Запуск в продакшене

### Вариант 1: Прямой запуск

```bash
# Запуск сервера
python webhook_server.py

# Или с gunicorn (рекомендуется)
gunicorn -w 4 -b 0.0.0.0:5000 webhook_server:app
```

### Вариант 2: Windows Service

```bash
# Установка как служба Windows
nssm install WhatsAppWebhook "C:\Python39\python.exe" "C:\path\to\webhook_server.py"
nssm set WhatsAppWebhook AppDirectory "C:\path\to\project"
nssm start WhatsAppWebhook
```

### Вариант 3: Docker (опционально)

```dockerfile
# Dockerfile
FROM python:3.10-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "webhook_server:app"]
```

```bash
# Сборка и запуск
docker build -t whatsapp-access .
docker run -d -p 5000:5000 --env-file .env whatsapp-access
```

---

## 🔧 Интеграция с Access

### 1. Импорт VBA модуля

1. Открыть Access
2. Перейти в **VBA Editor** (Alt+F11)
3. Вставить код из `access_integration.vba`
4. Сохранить модуль

### 2. Создание форм

**Форма отправки сообщений:**
```vba
Private Sub SendMessage_Click()
    Dim phoneNumber As String
    Dim messageText As String
    
    phoneNumber = FormatPhoneNumber(Me.txtPhoneNumber)
    messageText = Me.txtMessage
    
    If IsValidPhoneNumber(phoneNumber) Then
        If SendWhatsAppMessage(phoneNumber, messageText) Then
            MsgBox "Сообщение отправлено!", vbInformation
        Else
            MsgBox "Ошибка отправки!", vbCritical
        End If
    Else
        MsgBox "Неверный номер телефона!", vbExclamation
    End If
End Sub
```

**Форма просмотра сообщений:**
```vba
Private Sub Form_Load()
    Set Me.Recordset = GetRecentMessages(100)
End Sub
```

### 3. Автоматические макросы

**Макрос для обработки новых сообщений:**
```vba
Public Sub ProcessNewMessages()
    Dim rs As DAO.Recordset
    Set rs = GetUnreadMessages()
    
    Do While Not rs.EOF
        ' Обработка каждого непрочитанного сообщения
        UpdateMessageStatus rs!MessageID, "processed"
        rs.MoveNext
    Loop
    
    rs.Close
End Sub
```

---

## 🔒 Безопасность

### 1. SSL/TLS сертификат

```bash
# Получение бесплатного сертификата Let's Encrypt
certbot certonly --standalone -d your-domain.com

# Настройка в .env
SSL_CERT_PATH=/etc/letsencrypt/live/your-domain.com/fullchain.pem
SSL_KEY_PATH=/etc/letsencrypt/live/your-domain.com/privkey.pem
```

### 2. Firewall настройки

```bash
# Открыть только необходимые порты
netsh advfirewall firewall add rule name="WhatsApp Webhook" dir=in action=allow protocol=TCP localport=5000
```

### 3. Проверка подписи webhook

Система автоматически проверяет подпись webhook'ов от Meta для предотвращения подделки.

---

## 📊 Мониторинг и логирование

### 1. Логи системы

- **Файл логов:** `whatsapp_webhook.log`
- **База данных:** таблица `Logs`
- **Уровни:** INFO, WARNING, ERROR

### 2. Проверка состояния

```bash
# Проверка здоровья сервера
curl http://localhost:5000/health

# Просмотр логов
tail -f whatsapp_webhook.log
```

### 3. Метрики

```sql
-- Количество сообщений за день
SELECT COUNT(*) FROM Messages WHERE DATE(SentAt) = CURDATE();

-- Статистика по контактам
SELECT * FROM vw_ContactStats;

-- Ошибки за последний час
SELECT * FROM Logs WHERE LogLevel = 'ERROR' AND CreatedAt > DATE_SUB(NOW(), INTERVAL 1 HOUR);
```

---

## 🔄 Автоматизация

### 1. Планировщик задач Windows

```batch
# Создание задачи для автозапуска
schtasks /create /tn "WhatsApp Webhook" /tr "C:\Python39\python.exe C:\path\to\webhook_server.py" /sc onstart /ru SYSTEM
```

### 2. Автоматическая отправка

```vba
' Макрос для отправки запланированных сообщений
Public Sub SendScheduledMessages()
    Dim sql As String
    sql = "SELECT * FROM OutgoingMessages WHERE Status = 'pending' AND ScheduledAt <= Now()"
    
    Dim rs As DAO.Recordset
    Set rs = CurrentDb.OpenRecordset(sql)
    
    Do While Not rs.EOF
        If SendWhatsAppMessage(rs!ToUser, rs!MessageText) Then
            ' Обновление статуса
            CurrentDb.Execute "UPDATE OutgoingMessages SET Status = 'sent' WHERE ID = " & rs!ID
        End If
        rs.MoveNext
    Loop
    
    rs.Close
End Sub
```

---

## 🆘 Устранение неполадок

### Частые проблемы:

1. **Ошибка подключения к базе:**
   - Проверить ODBC DSN
   - Убедиться в доступности базы

2. **Webhook не получает сообщения:**
   - Проверить URL в настройках Meta
   - Убедиться в доступности сервера из интернета

3. **Ошибки отправки:**
   - Проверить Access Token
   - Убедиться в правильности номера телефона

### Команды диагностики:

```bash
# Проверка зависимостей
python -c "import pyodbc, flask, requests; print('OK')"

# Тест подключения к базе
python -c "import pyodbc; conn = pyodbc.connect('DSN=YourDSN'); print('Connected')"

# Проверка webhook
curl -X POST http://localhost:5000/webhook -H "Content-Type: application/json" -d '{"test": "data"}'
```

---

## 📞 Поддержка

### Логи для диагностики:
- `whatsapp_webhook.log` - логи сервера
- Таблица `Logs` - логи в базе данных
- Event Viewer Windows - системные события

### Контакты:
- Документация: `README.md`
- Структура таблиц: `table_structure.md`
- Примеры кода: `access_integration.vba`

---

## ✅ Чек-лист развертывания

- [ ] Установлены все зависимости
- [ ] Созданы таблицы в базе данных
- [ ] Настроен ODBC DSN
- [ ] Заполнен файл .env
- [ ] Настроен WhatsApp API
- [ ] Настроен webhook URL
- [ ] Проверена работа сервера
- [ ] Импортирован VBA модуль
- [ ] Созданы формы в Access
- [ ] Настроен SSL сертификат
- [ ] Настроен firewall
- [ ] Настроен автозапуск
- [ ] Проведено тестирование
- [ ] Настроен мониторинг

**Система готова к продакшн-использованию! 🎉** 
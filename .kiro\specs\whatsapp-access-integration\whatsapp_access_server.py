import os
import pyodbc
import requests
import logging
from flask import Flask, request, abort
from dotenv import load_dotenv
from datetime import datetime
from logging.handlers import RotatingFileHandler

# Загрузка переменных окружения из .env
load_dotenv()

ACCESS_DSN = os.getenv("ACCESS_DSN")
VERIFY_TOKEN = os.getenv("VERIFY_TOKEN")
MEDIA_TOKEN = os.getenv("MEDIA_TOKEN")
ALLOWED_IPS = os.getenv("ALLOWED_IPS", "").split(",")  # Список разрешённых IP через запятую

# Настройка логирования (ротация при 10 МБ, 5 архивов)
log_handler = RotatingFileHandler("whatsapp_integration.log", maxBytes=10*1024*1024, backupCount=5)
logging.basicConfig(handlers=[log_handler], level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")

def get_db_connection():
    try:
        return pyodbc.connect(f"DSN={ACCESS_DSN}")
    except Exception as e:
        logging.error(f"Ошибка подключения к Access: {e}")
        raise

def get_media_url(media_id):
    url = f"https://graph.facebook.com/v19.0/{media_id}"
    headers = {"Authorization": f"Bearer {MEDIA_TOKEN}"}
    try:
        res = requests.get(url, headers=headers, timeout=10)
        res.raise_for_status()
        media_info = res.json()
        return media_info.get("url")
    except Exception as e:
        logging.error(f"Ошибка получения media_url: {e}")
        return None

app = Flask(__name__)

@app.before_request
def limit_remote_addr():
    if ALLOWED_IPS and request.remote_addr not in ALLOWED_IPS:
        logging.warning(f"Попытка доступа с неразрешённого IP: {request.remote_addr}")
        abort(403)

@app.route('/health', methods=['GET'])
def health():
    # Healthcheck endpoint для мониторинга
    return "OK", 200

@app.route('/webhook', methods=['GET', 'POST'])
def webhook():
    if request.method == 'GET':
        if request.args.get('hub.verify_token') == VERIFY_TOKEN:
            return request.args.get('hub.challenge')
        return 'Forbidden', 403

    # Проверка типа контента
    if not request.is_json:
        logging.warning("Некорректный Content-Type")
        return 'Bad Request', 400

    data = request.json
    logging.info(f"Получен webhook: {data}")

    try:
        conn = get_db_connection()
        cursor = conn.cursor()
    except Exception:
        return 'DB Error', 500

    try:
        for entry in data.get("entry", []):
            for change in entry.get("changes", []):
                value = change.get("value", {})

                # Обработка сообщений
                messages = value.get("messages", [])
                for msg in messages:
                    try:
                        msg_id = msg['id']
                        msg_type = msg['type']
                        from_user = msg['from']
                        text = msg.get("text", {}).get("body", "")
                        timestamp = datetime.utcfromtimestamp(int(msg["timestamp"]))
                        direction = "inbound"

                        media_url = None
                        if msg_type in ["image", "audio", "video", "document"]:
                            media_id = msg[msg_type]["id"]
                            media_url = get_media_url(media_id)

                        cursor.execute("""
                            INSERT INTO Messages (MessageID, ChatID, FromUser, Text, Type, MediaURL, Timestamp, Direction)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        """, msg_id, from_user, from_user, text, msg_type, media_url, timestamp, direction)
                        conn.commit()
                        logging.info(f"Сохранено сообщение {msg_id}")
                    except Exception as e:
                        logging.error(f"Ошибка при обработке сообщения: {e}")

                # Обработка звонков
                statuses = value.get("statuses", [])
                for status in statuses:
                    if status.get("type") == "call":
                        try:
                            call_id = status["id"]
                            call_type = status.get("call", {}).get("type")
                            call_status = status.get("call", {}).get("status")
                            from_user = status["recipient_id"]
                            timestamp = datetime.utcfromtimestamp(int(status["timestamp"]))
                            direction = "inbound" if from_user else "outbound"

                            cursor.execute("""
                                INSERT INTO Calls (CallID, FromUser, Direction, Type, Status, Timestamp)
                                VALUES (?, ?, ?, ?, ?, ?)
                            """, call_id, from_user, direction, call_type, call_status, timestamp)
                            conn.commit()
                            logging.info(f"Сохранён звонок {call_id}")
                        except Exception as e:
                            logging.error(f"Ошибка при обработке звонка: {e}")

    except Exception as e:
        logging.error(f"Ошибка обработки webhook: {e}")
        return 'Internal Error', 500
    finally:
        try:
            conn.close()
        except Exception:
            pass

    return '', 200

if __name__ == '__main__':
    # Используй реальный SSL-сертификат в проде!
    app.run(host='0.0.0.0', port=443, ssl_context=('cert.pem', 'key.pem'))
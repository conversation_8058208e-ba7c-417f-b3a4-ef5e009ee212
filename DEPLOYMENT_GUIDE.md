# Руководство по развертыванию WhatsApp Webhook сервера

## Оглавление
1. [Обзор системы](#обзор-системы)
2. [Требования](#требования)
3. [Локальная разработка](#локальная-разработка)
4. [Развертывание на сервере](#развертывание-на-сервере)
5. [Настройка базы данных](#настройка-базы-данных)
6. [Настройка WhatsApp API](#настройка-whatsapp-api)
7. [Мониторинг и логи](#мониторинг-и-логи)
8. [Устранение неполадок](#устранение-неполадок)

## Обзор системы

WhatsApp Webhook сервер предназначен для интеграции WhatsApp Business API с базой данных SQL Server. Система обрабатывает входящие сообщения WhatsApp и сохраняет их в базе данных для дальнейшей обработки.

### Архитектура
```
WhatsApp Business API → Webhook Server (Flask) → SQL Server Database
                           ↓
                    Supervisor (процесс-менеджер)
                           ↓
                    Nginx (опционально, для продакшена)
```

## Требования

### Системные требования
- **Операционная система**: Ubuntu 20.04+ или Windows Server 2019+
- **Python**: 3.8+
- **Память**: минимум 1GB RAM
- **Дисковое пространство**: минимум 5GB

### Программные зависимости
- Python 3.8+
- pip (менеджер пакетов Python)
- SQL Server или SQL Server Express
- ODBC Driver 18 for SQL Server

### Сетевые требования
- Порт 8080 (для webhook сервера)
- Порт 1433 (для подключения к SQL Server)
- Исходящие HTTPS соединения (для WhatsApp API)

## Локальная разработка

### 1. Клонирование и настройка проекта

```bash
# Перейдите в директорию проекта
cd "C:\Users\<USER>\Documents\Воркзилла\написать код на VBA Access"

# Создайте виртуальное окружение
python -m venv venv

# Активируйте виртуальное окружение
# Windows:
venv\Scripts\activate
# Linux:
source venv/bin/activate

# Установите зависимости
pip install -r requirements.txt
```

### 2. Настройка переменных окружения

Создайте файл `.env` на основе `.env.example`:

```bash
# Скопируйте пример конфигурации
copy .env.example .env
```

Отредактируйте `.env` файл:

```env
# Строка подключения к SQL Server
ACCESS_DSN=DRIVER={ODBC Driver 18 for SQL Server};SERVER=*************,1433;DATABASE=whatsapp_integration;UID=WhatsApp_ferroli;PWD=your_password;TrustServerCertificate=yes;

# WhatsApp API настройки
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_ACCESS_TOKEN=your_access_token
WHATSAPP_VERIFY_TOKEN=your_verify_token

# Flask настройки
FLASK_ENV=development
FLASK_DEBUG=True
```

### 3. Запуск локального сервера

```bash
# Установите переменную окружения для строки подключения
$env:ACCESS_DSN="DRIVER={SQL Server};SERVER=*************,1433;DATABASE=whatsapp_integration;UID=WhatsApp_ferroli;PWD=your_password"

# Запустите сервер
python webhook_server.py
```

Сервер будет доступен по адресу: http://localhost:5000

### 4. Тестирование подключения

```bash
# Тест подключения к базе данных
curl http://localhost:5000/db-test

# Тест webhook endpoint
curl -X GET http://localhost:5000/webhook?hub.verify_token=your_verify_token&hub.challenge=test&hub.mode=subscribe
```

## Развертывание на сервере

### Автоматическое развертывание

Используйте готовые скрипты для автоматического развертывания:

```bash
# Сделайте скрипты исполняемыми
chmod +x deploy_remote.sh
chmod +x upload_and_deploy.sh

# Запустите развертывание
./upload_and_deploy.sh
```

### Ручное развертывание

#### 1. Подготовка сервера

```bash
# Обновите систему
sudo apt update

# Установите Python и pip
sudo apt install -y python3 python3-pip python3-venv

# Установите ODBC Driver для SQL Server
curl https://packages.microsoft.com/keys/microsoft.asc | sudo apt-key add -
curl https://packages.microsoft.com/config/ubuntu/20.04/prod.list | sudo tee /etc/apt/sources.list.d/msprod.list
sudo apt update
sudo ACCEPT_EULA=Y apt install -y msodbcsql18

# Установите Supervisor
sudo apt install -y supervisor

# Создайте пользователя для приложения
sudo useradd -m -s /bin/bash whatsapp
```

#### 2. Установка приложения

```bash
# Создайте директорию проекта
sudo mkdir -p /opt/whatsapp-webhook
sudo chown whatsapp:whatsapp /opt/whatsapp-webhook

# Переключитесь на пользователя whatsapp
sudo su - whatsapp

# Создайте виртуальное окружение
cd /opt/whatsapp-webhook
python3 -m venv venv
source venv/bin/activate

# Установите зависимости
pip install flask pyodbc python-dotenv requests gunicorn
```

#### 3. Загрузка кода

```bash
# Скопируйте файлы на сервер
scp webhook_server.py root@your_server:/opt/whatsapp-webhook/
scp requirements.txt root@your_server:/opt/whatsapp-webhook/
scp .env root@your_server:/opt/whatsapp-webhook/

# Установите права доступа
sudo chown -R whatsapp:whatsapp /opt/whatsapp-webhook
```

#### 4. Настройка Supervisor

Создайте файл `/etc/supervisor/conf.d/whatsapp-webhook.conf`:

```ini
[program:whatsapp-webhook]
command=/opt/whatsapp-webhook/venv/bin/gunicorn --bind 0.0.0.0:8080 --workers 2 webhook_server:app
directory=/opt/whatsapp-webhook
user=whatsapp
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/whatsapp-webhook.log
environment=PATH="/opt/whatsapp-webhook/venv/bin"
```

#### 5. Запуск сервиса

```bash
# Обновите конфигурацию Supervisor
sudo supervisorctl reread
sudo supervisorctl update

# Запустите сервис
sudo supervisorctl start whatsapp-webhook

# Проверьте статус
sudo supervisorctl status whatsapp-webhook
```

## Настройка базы данных

### 1. Создание базы данных

```sql
-- Создайте базу данных
CREATE DATABASE whatsapp_integration;
GO

-- Используйте базу данных
USE whatsapp_integration;
GO
```

### 2. Создание пользователя

```sql
-- Создайте логин
CREATE LOGIN WhatsApp_ferroli WITH PASSWORD = 'your_strong_password';
GO

-- Создайте пользователя в базе данных
USE whatsapp_integration;
CREATE USER WhatsApp_ferroli FOR LOGIN WhatsApp_ferroli;
GO

-- Предоставьте права
ALTER ROLE db_datareader ADD MEMBER WhatsApp_ferroli;
ALTER ROLE db_datawriter ADD MEMBER WhatsApp_ferroli;
GO
```

### 3. Создание таблиц

Выполните скрипт `whatsapp_access_tables.sql`:

```bash
# Выполните SQL скрипт
sqlcmd -S your_server -d whatsapp_integration -i whatsapp_access_tables.sql
```

### 4. Проверка подключения

```bash
# Тест подключения
sqlcmd -S your_server,1433 -U WhatsApp_ferroli -P your_password -d whatsapp_integration -Q "SELECT 1 as test"
```

## Настройка WhatsApp API

### 1. Получение учетных данных

1. Зарегистрируйтесь в [Facebook for Developers](https://developers.facebook.com/)
2. Создайте приложение WhatsApp Business
3. Получите следующие данные:
   - Phone Number ID
   - Access Token
   - Verify Token (создайте свой)

### 2. Настройка Webhook

1. В панели WhatsApp Business API укажите URL webhook:
   ```
   https://your-server.com:8080/webhook
   ```

2. Укажите Verify Token, который вы задали в `.env`

3. Подпишитесь на события:
   - messages
   - message_deliveries
   - message_reads

### 3. Обновление конфигурации

Обновите файл `.env` с реальными данными:

```env
WHATSAPP_PHONE_NUMBER_ID=123456789012345
WHATSAPP_ACCESS_TOKEN=EAAxxxxxxxxxxxxx
WHATSAPP_VERIFY_TOKEN=your_custom_verify_token
```

## Мониторинг и логи

### Просмотр логов

```bash
# Логи приложения
sudo tail -f /var/log/whatsapp-webhook.log

# Логи Supervisor
sudo tail -f /var/log/supervisor/supervisord.log

# Системные логи
sudo journalctl -u supervisor -f
```

### Мониторинг статуса

```bash
# Статус сервиса
sudo supervisorctl status whatsapp-webhook

# Проверка процессов
ps aux | grep gunicorn

# Проверка портов
netstat -tlnp | grep 8080
```

### Проверка работоспособности

```bash
# Тест endpoint'ов
curl http://localhost:8080/health
curl http://localhost:8080/db-test

# Проверка с внешнего сервера
curl http://your-server-ip:8080/health
```

## Устранение неполадок

### Проблемы с подключением к базе данных

#### Ошибка 18456 (Login failed)
```bash
# Проверьте учетные данные
sqlcmd -S server -U user -P password -Q "SELECT 1"

# Проверьте права пользователя
# В SQL Server Management Studio:
# Security → Logins → WhatsApp_ferroli → Properties → User Mapping
```

#### Ошибка таймаута подключения
```bash
# Проверьте сетевое подключение
telnet your-server 1433

# Проверьте настройки SQL Server
# SQL Server Configuration Manager → SQL Server Network Configuration → Protocols
# Убедитесь, что TCP/IP включен
```

### Проблемы с сервисом

#### Сервис не запускается
```bash
# Проверьте логи
sudo tail -50 /var/log/whatsapp-webhook.log

# Проверьте конфигурацию Supervisor
sudo supervisorctl reread
sudo supervisorctl update

# Перезапустите сервис
sudo supervisorctl restart whatsapp-webhook
```

#### Порт занят
```bash
# Найдите процесс, использующий порт
sudo lsof -i :8080

# Остановите процесс
sudo kill -9 PID
```

### Проблемы с WhatsApp API

#### Webhook не получает сообщения
1. Проверьте URL webhook в настройках Facebook
2. Убедитесь, что сервер доступен извне
3. Проверьте Verify Token
4. Проверьте подписки на события

#### Ошибки отправки сообщений
1. Проверьте Access Token
2. Проверьте Phone Number ID
3. Проверьте лимиты API

### Команды для диагностики

```bash
# Полная диагностика системы
echo "=== Статус сервиса ==="
sudo supervisorctl status

echo "=== Процессы ==="
ps aux | grep -E "(python|gunicorn)"

echo "=== Порты ==="
netstat -tlnp | grep -E "(8080|1433)"

echo "=== Логи (последние 20 строк) ==="
sudo tail -20 /var/log/whatsapp-webhook.log

echo "=== Тест подключения к БД ==="
curl -s http://localhost:8080/db-test

echo "=== Тест webhook ==="
curl -s "http://localhost:8080/webhook?hub.verify_token=test&hub.challenge=test&hub.mode=subscribe"
```

## Обновление приложения

### 1. Обновление кода

```bash
# Загрузите новый код
scp webhook_server.py root@your-server:/opt/whatsapp-webhook/

# Установите права
sudo chown whatsapp:whatsapp /opt/whatsapp-webhook/webhook_server.py

# Перезапустите сервис
sudo supervisorctl restart whatsapp-webhook
```

### 2. Обновление зависимостей

```bash
# Активируйте виртуальное окружение
sudo su - whatsapp
cd /opt/whatsapp-webhook
source venv/bin/activate

# Обновите зависимости
pip install -r requirements.txt --upgrade

# Перезапустите сервис
sudo supervisorctl restart whatsapp-webhook
```

## Резервное копирование

### База данных
```bash
# Создание резервной копии
sqlcmd -S server -Q "BACKUP DATABASE whatsapp_integration TO DISK = 'C:\Backup\whatsapp_integration.bak'"

# Восстановление
sqlcmd -S server -Q "RESTORE DATABASE whatsapp_integration FROM DISK = 'C:\Backup\whatsapp_integration.bak'"
```

### Конфигурация
```bash
# Резервное копирование конфигурации
sudo cp /opt/whatsapp-webhook/.env /backup/
sudo cp /etc/supervisor/conf.d/whatsapp-webhook.conf /backup/
```

## Безопасность

### Рекомендации по безопасности

1. **Используйте сильные пароли** для базы данных
2. **Ограничьте доступ** к серверу по IP
3. **Используйте HTTPS** для webhook URL
4. **Регулярно обновляйте** систему и зависимости
5. **Мониторьте логи** на предмет подозрительной активности

### Настройка файрвола

```bash
# Разрешите только необходимые порты
sudo ufw allow 22    # SSH
sudo ufw allow 8080  # Webhook
sudo ufw enable
```

## Контакты и поддержка

При возникновении проблем:
1. Проверьте логи приложения
2. Выполните диагностические команды
3. Обратитесь к разделу "Устранение неполадок"
4. Создайте issue в репозитории проекта

---

**Версия документации**: 1.0  
**Дата обновления**: 2025-08-05  
**Автор**: WhatsApp Integration Team
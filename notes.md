# Заметки

## Ключевые улучшения WhatsApp-Access интеграции

### 1. Безопасность и конфигурация
- Перевести `WEBHOOK_URL` и другие константы в таблицу настроек Access или `.env`
- В VBA читать через DLookup/DAO вместо хардкода
- В Python включить HTTPS (Flask + сертификат) и проверку подписи webhook-запросов
- **Причина**: упрощает перенос между средами и исключает хардкод URL

### 2. Надёжность отправки сообщений
- В `SendWhatsAppMessage` добавить повторную попытку (retry с экспоненциальной задержкой)
- Добавить тайм-аут `XMLHTTP.setTimeouts`
- **Причина**: снижает риск потери сообщений при временных сбоях сети

### 3. Логирование и мониторинг
- В VBA создать функцию `LogMessage` с записью в таблицу `Logs`
- В Python добавить метрики Prometheus/Healthcheck-эндпоинт
- Отслеживать количество ошибок и время ответа
- **Причина**: симметрия с Python-логами, упрощённый аудит

### 4. Производительность базы данных
- Заменить `CurrentDb.OpenRecordset` и `CreateQueryDef("")` на сохранённые запросы
- В Python использовать `with`-контекст для cursor-операций
- **Причина**: Access кеширует планы для сохранённых запросов, снижается нагрузка

### 5. Обработка больших вложений
- При приёме медиа скачивать файл в S3/Azure Blob вместо хранения URL WhatsApp
- Добавить фоновую задачу для обработки
- **Причина**: ссылки WhatsApp истекают ~14 дней, файлы нужны дольше

### 6. Покрытие тестами
- Добавить unit-тесты Python (pytest, fixtures с SQLite/Fake-ODBC)
- VBA-тесты (Rubberduck)
- **Причина**: автоматическая проверка регрессий при изменениях

### 7. Очередь исходящих сообщений
- Реализовать отдельный сервис-воркер для выборки из `OutgoingMessages`
- Отправка через API batch-ом
- **Причина**: разгрузка VBA-клиента и ровное распределение нагрузки

## Приоритет внедрения
1. **Высокий**: Безопасность, надёжность отправки
2. **Средний**: Логирование, производительность БД
3. **Низкий**: Тесты, очередь сообщений

## Быстрые победы
- Вынести URL в настройки
- Добавить retry в SendWhatsAppMessage
- Создать таблицу Logs для VBA

---
*Обновлено: вручную*

## Дополнительно к плану

1) Конфигурация и секреты
- Использовать .env на стороне Python (пример есть: `.env.example`). Создать `.env` и вынести строку подключения, порт, DEBUG, секрет подписи вебхука.
- Для Access — добавить таблицу `Settings(Key, Value, UpdatedAt)` и вынести `WEBHOOK_URL`, `TIMEOUT_MS`, `RETRY_LIMIT`.

2) Валидация входящих данных во Flask
- Добавить проверку payload вебхуков: обязательные поля, типы, длины (pydantic или ручная валидация).

3) Идемпотентность вебхуков
- Уникальный индекс на `message_id` (или `from+timestamp`) в таблице входящих. Перед вставкой проверять существование.

4) Тайм-ауты и повторы в VBA
- В `SendWhatsAppMessage`: `setTimeouts` (например, 2000, 2000, 5000, 15000) и 2–3 повтора с backoff (1s, 3s, 7s). Логировать каждую попытку.

5) Единый формат логов
- VBA: `Logs(Id, Level, Source, Message, ContextJSON, CreatedAt)`.
- Python: JSON-логгер с полями `level`, `event`, `correlation_id`, `contact_id`, `message_id`.

6) Здоровье сервиса
- Эндпоинт `/healthz` (проверка БД, версия приложения) и базовые `/metrics`.

7) Миграции БД
- SQL Server: скрипты миграций + таблица `SchemaVersions`.
- Access: макрос/скрипт для первичного создания таблиц.

8) Мини-очередь исходящих
- В `OutgoingMessages` добавить `status`, `attempt_count`, `next_attempt_at`.
- Небольшой воркер на Python, который периодически отправляет queued и обновляет статус.

9) Медиа
- Поле `media_expires_at`, фоновая загрузка в S3/Azure/локально, обновление `media_local_url`.

10) Безопасность webhook
- HMAC-подпись (заголовок `X-Signature`) и rate limiting (Flask-Limiter/nginx/IIS).

11) Тестирование
- Python: pytest (БД-слой с моками pyodbc, обработчик вебхука, воркер очереди).
- VBA: Rubberduck для ключевых функций.

12) Наблюдаемость
- Пробрасывать `correlation_id` через все логи. Простая HTML-дашборд-страница: входящие за сутки, ошибки, среднее время ответа, размер очереди.

13) Документация
- Обновить README/PRODUCTION_README: переменные окружения, health/metrics, секрет подписи, схема логов, структура таблиц. Добавить чек-лист спринтов в notes.md.
# Руководство по настройке WhatsApp интеграции с SQLite

## 📋 Обзор системы

Эта система обеспечивает интеграцию WhatsApp Business API с локальной базой данных SQLite для:
- Получения и сохранения входящих сообщений
- Отправки исходящих сообщений
- Управления контактами
- Ведения логов операций

## 🗂️ Структура файлов

```
├── webhook_server.py          # Основной сервер Flask
├── create_test_db.py         # Скрипт создания тестовой БД
├── view_database.py          # Просмотр содержимого БД
├── whatsapp_test.db          # База данных SQLite
├── .env                      # Переменные окружения
├── whatsapp_access_tables.sql # SQL схема для справки
└── requirements.txt          # Зависимости Python
```

## 🚀 Быстрый старт

### 1. Создание базы данных
```bash
python create_test_db.py
```

### 2. Настройка переменных окружения
Отредактируйте файл `.env`:
```env
# WhatsApp Business API настройки
WHATSAPP_ACCESS_TOKEN=your_real_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id
WEBHOOK_VERIFY_TOKEN=your_verify_token

# База данных SQLite
SQLITE_DB_PATH=whatsapp_test.db

# Настройки сервера
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_DEBUG=true
```

### 3. Запуск сервера
```bash
python webhook_server.py
```

Сервер будет доступен по адресу: `http://localhost:5000`

## 🔧 API Эндпоинты

### Проверка состояния
```http
GET /health
```
Возвращает статус подключения к базе данных.

### Верификация webhook
```http
GET /webhook?hub.verify_token=TOKEN&hub.challenge=CHALLENGE&hub.mode=subscribe
```
Используется Meta для верификации webhook.

### Получение входящих сообщений
```http
POST /webhook
```
Обрабатывает входящие сообщения от WhatsApp.

### Отправка сообщений
```http
POST /send
Content-Type: application/json

{
  "to": "79001234567",
  "message": "Текст сообщения"
}
```

## 📊 Структура базы данных

### Таблица Messages (входящие сообщения)
- `MessageID` - ID сообщения от WhatsApp
- `ChatID` - ID чата
- `FromUser` - номер отправителя
- `ToUser` - номер получателя (бизнес-аккаунт)
- `Text` - текст сообщения
- `MessageType` - тип сообщения (text, image, etc.)
- `SentAt` - время отправки
- `Status` - статус сообщения
- `RawData` - полные данные webhook в JSON

### Таблица OutgoingMessages (исходящие сообщения)
- `ID` - автоинкремент ID
- `ToUser` - номер получателя
- `MessageText` - текст сообщения
- `Status` - статус отправки
- `WhatsAppMessageID` - ID от WhatsApp API
- `CreatedAt` - время создания

### Таблица Contacts (контакты)
- `PhoneNumber` - номер телефона (первичный ключ)
- `ContactName` - имя контакта
- `Company` - компания
- `MessageCount` - количество сообщений
- `LastMessageAt` - время последнего сообщения
- `IsActive` - активен ли контакт

### Таблица Settings (настройки)
- `SettingKey` - ключ настройки
- `SettingValue` - значение настройки
- `Description` - описание

### Таблица Logs (логи)
- `ID` - автоинкремент ID
- `LogLevel` - уровень лога (INFO, WARNING, ERROR)
- `Message` - текст сообщения
- `Source` - источник лога
- `CreatedAt` - время создания

## 🛠️ Полезные команды

### Просмотр содержимого базы данных
```bash
python view_database.py
```

### Тестирование отправки сообщения
```bash
curl -X POST http://localhost:5000/send \
  -H "Content-Type: application/json" \
  -d '{"to": "79001234567", "message": "Тест"}'
```

### Проверка состояния сервера
```bash
curl http://localhost:5000/health
```

## 🔒 Безопасность

1. **Верификация webhook**: Используйте `WEBHOOK_VERIFY_TOKEN` для защиты endpoint
2. **Подпись webhook**: Настройте `WHATSAPP_APP_SECRET` для проверки подписи
3. **HTTPS**: В продакшене используйте HTTPS
4. **Firewall**: Ограничьте доступ к серверу

## 📝 Логирование

Логи записываются в:
- Консоль (stdout)
- Файл `whatsapp_webhook.log`
- Таблица `Logs` в базе данных

## 🚨 Устранение неполадок

### Ошибка подключения к базе данных
```
Ошибка: База данных не найдена
```
**Решение**: Запустите `python create_test_db.py`

### Ошибка NOT NULL constraint
```
NOT NULL constraint failed: Messages.ToUser
```
**Решение**: Убедитесь, что webhook содержит метаданные с `display_phone_number`

### Сервер не запускается
```
Address already in use
```
**Решение**: Остановите другие процессы на порту 5000 или измените порт в `.env`

## 🔄 Интеграция с Access

Для интеграции с Microsoft Access:

1. Используйте ODBC драйвер для SQLite
2. Создайте связанные таблицы в Access
3. Настройте VBA макросы для автоматической обработки

### Пример VBA кода для чтения сообщений:
```vba
Sub ReadMessages()
    Dim db As Database
    Dim rs As Recordset
    
    Set db = OpenDatabase("whatsapp_test.db", False, False, "ODBC;")
    Set rs = db.OpenRecordset("SELECT * FROM Messages ORDER BY SentAt DESC")
    
    Do While Not rs.EOF
        Debug.Print rs!FromUser & ": " & rs!Text
        rs.MoveNext
    Loop
    
    rs.Close
    db.Close
End Sub
```

## 📞 Поддержка

При возникновении проблем:
1. Проверьте логи в `whatsapp_webhook.log`
2. Используйте `python view_database.py` для диагностики БД
3. Проверьте статус через `/health` endpoint

## 📈 Мониторинг

Для мониторинга системы:
- Регулярно проверяйте `/health` endpoint
- Отслеживайте размер файла логов
- Мониторьте размер базы данных SQLite
- Проверяйте статистику сообщений через `view_database.py`
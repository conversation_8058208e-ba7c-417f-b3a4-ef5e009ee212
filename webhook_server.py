#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WhatsApp Webhook Server для интеграции с Microsoft Access
Продакшн-версия с полной обработкой ошибок и логированием
"""

import os
import json
import logging
import sqlite3
import requests
from datetime import datetime
from flask import Flask, request, jsonify
from dotenv import load_dotenv
import hashlib
import hmac
import time
from typing import Dict, Any, Optional

# Загрузка переменных окружения
load_dotenv()

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('whatsapp_webhook.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

class WhatsAppDatabase:
    """Класс для работы с базой данных SQLite"""
    
    def __init__(self, database_path: str):
        self.database_path = database_path
        self.connection = None
        self.cursor = None
    
    def connect(self) -> bool:
        """Подключение к базе данных"""
        try:
            if not os.path.exists(self.database_path):
                logger.error(f"База данных не найдена: {self.database_path}")
                return False
            
            self.connection = sqlite3.connect(self.database_path)
            self.connection.row_factory = sqlite3.Row
            self.cursor = self.connection.cursor()
            logger.info("Успешное подключение к базе данных SQLite")
            return True
        except Exception as e:
            logger.error(f"Ошибка подключения к базе данных: {e}")
            return False
    
    def disconnect(self):
        """Отключение от базы данных"""
        try:
            if self.cursor:
                self.cursor.close()
                self.cursor = None
            if self.connection:
                self.connection.close()
                self.connection = None
        except Exception:
            pass
    
    def log_message(self, level: str, message: str, source: str = "webhook"):
        """Запись лога в базу данных"""
        try:
            self.cursor.execute(
                "INSERT INTO Logs (LogLevel, Message, Source) VALUES (?, ?, ?)",
                (level, message, source)
            )
            self.connection.commit()
        except Exception as e:
            logger.error(f"Ошибка записи лога в БД: {e}")
    
    def save_message(self, message_data: Dict[str, Any], metadata: Dict[str, Any] = None) -> bool:
        """Сохранение входящего сообщения в базу"""
        try:
            # Извлечение данных из webhook
            message_id = message_data.get('id')
            chat_id = message_data.get('from')  # Используем номер отправителя как ChatID
            from_user = message_data.get('from')
            
            # ToUser берем из метаданных webhook (номер бизнес-аккаунта)
            to_user = metadata.get('display_phone_number') if metadata else 'business_account'
            
            text = message_data.get('text', {}).get('body', '') if message_data.get('text') else ''
            message_type = message_data.get('type', 'text')
            timestamp = datetime.fromtimestamp(int(message_data.get('timestamp', time.time())))
            
            # Сохранение сообщения
            self.cursor.execute("""
                INSERT INTO Messages (MessageID, ChatID, FromUser, ToUser, Text, MessageType, SentAt, RawData)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (message_id, chat_id, from_user, to_user, text, message_type, timestamp, json.dumps(message_data)))
            
            # Обновление статистики контакта
            self.update_contact_stats(from_user, timestamp)
            
            self.connection.commit()
            logger.info(f"Сообщение {message_id} от {from_user} к {to_user} сохранено в базу")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка сохранения сообщения: {e}")
            if self.connection:
                self.connection.rollback()
            return False
    
    def update_contact_stats(self, phone_number: str, last_message_time: datetime):
        """Обновление статистики контакта"""
        try:
            # Проверяем, существует ли контакт
            self.cursor.execute("SELECT PhoneNumber FROM Contacts WHERE PhoneNumber = ?", (phone_number,))
            if not self.cursor.fetchone():
                # Создаем новый контакт
                self.cursor.execute("""
                    INSERT INTO Contacts (PhoneNumber, ContactName, CreatedAt, LastMessageAt, MessageCount)
                    VALUES (?, ?, ?, ?, 1)
                """, (phone_number, f"Контакт {phone_number}", datetime.now(), last_message_time))
            else:
                # Обновляем существующий контакт
                self.cursor.execute("""
                    UPDATE Contacts 
                    SET LastMessageAt = ?, MessageCount = MessageCount + 1
                    WHERE PhoneNumber = ?
                """, (last_message_time, phone_number))
            
            self.connection.commit()
            
        except Exception as e:
            logger.error(f"Ошибка обновления статистики контакта: {e}")
    
    def get_setting(self, key: str) -> Optional[str]:
        """Получение настройки из базы"""
        try:
            self.cursor.execute("SELECT SettingValue FROM Settings WHERE SettingKey = ?", (key,))
            result = self.cursor.fetchone()
            return result[0] if result else None
        except Exception as e:
            logger.error(f"Ошибка получения настройки {key}: {e}")
            return None

# Тестовый эндпоинт для проверки подключения к БД
@app.route('/db-test', methods=['GET'])
def db_test():
    """Проверка подключения к базе и базового запроса."""
    try:
        db_path = os.getenv('SQLITE_DB_PATH', 'whatsapp_test.db')
        if not os.path.exists(db_path):
            return jsonify({"ok": False, "error": f"База данных не найдена: {db_path}"}), 500
        test_db = WhatsAppDatabase(db_path)
        if not test_db.connect():
            return jsonify({"ok": False, "error": "Не удалось подключиться к базе"}), 500
        try:
            # Минимальная проверка возможности выполнять запросы
            test_db.cursor.execute("SELECT 1")
            row = test_db.cursor.fetchone()
            result = row[0] if row else None
            logger.info("DB test SELECT 1 => %s", result)
            return jsonify({"ok": True, "result": int(result)}), 200
        finally:
            test_db.disconnect()
    except Exception as e:
        logger.error(f"DB test error: {e}")
        return jsonify({"ok": False, "error": str(e)}), 500

class WhatsAppAPI:
    """Класс для работы с WhatsApp Cloud API"""
    
    def __init__(self, phone_number_id: str, access_token: str):
        self.phone_number_id = phone_number_id
        self.access_token = access_token
        self.base_url = "https://graph.facebook.com/v18.0"
    
    def send_message(self, to_number: str, message_text: str) -> Dict[str, Any]:
        """Отправка сообщения через WhatsApp API"""
        try:
            # Проверка тестового режима
            if os.getenv('TEST_MODE', '0') == '1':
                fake_id = f"test_{int(time.time())}"
                logger.info(f"[TEST_MODE] send to {to_number}: {message_text}")
                return {"messages": [{"id": fake_id}], "to": to_number, "test_mode": True}
            
            url = f"{self.base_url}/{self.phone_number_id}/messages"
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            data = {
                "messaging_product": "whatsapp",
                "to": to_number,
                "type": "text",
                "text": {"body": message_text}
            }
            
            response = requests.post(url, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"Сообщение отправлено: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Ошибка отправки сообщения: {e}")
            return {"error": str(e)}

# Инициализация объектов
db = WhatsAppDatabase(os.getenv('ACCESS_DSN', ''))
whatsapp_api = None

def initialize_whatsapp_api():
    """Инициализация WhatsApp API с настройками из базы"""
    global whatsapp_api
    if db.connect():
        phone_number_id = db.get_setting('whatsapp_phone_number_id')
        access_token = db.get_setting('whatsapp_access_token')
        db.disconnect()
    else:
        phone_number_id = None
        access_token = None
    
    # Fallback на переменные окружения
    phone_number_id = phone_number_id or os.getenv('WHATSAPP_PHONE_NUMBER_ID', '')
    access_token = access_token or os.getenv('WHATSAPP_ACCESS_TOKEN', '')
    
    if phone_number_id and access_token:
        whatsapp_api = WhatsAppAPI(phone_number_id, access_token)
        logger.info("WhatsApp API инициализирован")
    else:
        logger.warning("Не удалось получить настройки WhatsApp API")

@app.route('/webhook', methods=['GET'])
def verify_webhook():
    """Верификация webhook от Meta"""
    try:
        mode = request.args.get('hub.mode')
        token = request.args.get('hub.verify_token')
        challenge = request.args.get('hub.challenge')
        
        if db.connect():
            verify_token = db.get_setting('webhook_verify_token')
            db.disconnect()
        else:
            verify_token = os.getenv('WEBHOOK_VERIFY_TOKEN', '')
        
        if mode == 'subscribe' and token == verify_token:
            logger.info("Webhook верифицирован успешно")
            return challenge
        else:
            logger.warning("Неверная верификация webhook")
            return 'Forbidden', 403
            
    except Exception as e:
        logger.error(f"Ошибка верификации webhook: {e}")
        return 'Internal Server Error', 500

@app.route('/webhook', methods=['POST'])
def handle_webhook():
    """Обработка входящих сообщений от WhatsApp"""
    try:
        # Проверка подписи отключена для тестирования
        # if not verify_signature(request):
        #     logger.warning("Неверная подпись webhook")
        #     return 'Unauthorized', 401
        
        # Получение и проверка JSON данных
        try:
            data = request.get_json()
            if not data:
                logger.error("Пустые данные webhook")
                return 'Bad Request', 400
        except Exception as json_error:
            logger.error(f"Ошибка парсинга JSON: {json_error}")
            return 'Bad Request', 400
            
        logger.info(f"Получен webhook: {json.dumps(data, indent=2)}")
        
        if not db.connect():
            logger.error("Не удалось подключиться к базе данных")
            return 'Internal Server Error', 500
        
        # Обработка входящих сообщений
        for entry in data.get('entry', []):
            for change in entry.get('changes', []):
                if change.get('value', {}).get('messages'):
                    # Получаем метаданные из webhook
                    metadata = change.get('value', {}).get('metadata', {})
                    
                    for message in change['value']['messages']:
                        # Сохранение сообщения с метаданными
                        if db.save_message(message, metadata):
                            # Проверка автоответа
                            if should_send_auto_reply():
                                auto_reply_text = db.get_setting('auto_reply_message')
                                if auto_reply_text and whatsapp_api:
                                    whatsapp_api.send_message(message['from'], auto_reply_text)
                                    logger.info(f"Отправлен автоответ пользователю {message['from']}")
        
        db.disconnect()
        return 'OK', 200
        
    except Exception as e:
        logger.error(f"Ошибка обработки webhook: {e}")
        if db.connection:
            db.disconnect()
        return 'Internal Server Error', 500

def verify_signature(request) -> bool:
    """Проверка подписи webhook (для безопасности)"""
    try:
        signature = request.headers.get('X-Hub-Signature-256', '')
        if not signature:
            return True  # Пропускаем, если подпись не настроена
        
        app_secret = os.getenv('WHATSAPP_APP_SECRET', '')
        if not app_secret:
            return True  # Пропускаем, если секрет не настроен
        
        expected_signature = 'sha256=' + hmac.new(
            app_secret.encode('utf-8'),
            request.data,
            hashlib.sha256
        ).hexdigest()
        
        return hmac.compare_digest(signature, expected_signature)
        
    except Exception as e:
        logger.error(f"Ошибка проверки подписи: {e}")
        return False

def should_send_auto_reply() -> bool:
    """Проверка, нужно ли отправлять автоответ"""
    try:
        auto_reply_enabled = db.get_setting('auto_reply_enabled')
        return auto_reply_enabled == '1'
    except:
        return False

@app.route('/health', methods=['GET'])
def health_check():
    """Проверка состояния сервера"""
    try:
        if db.connect():
            db.disconnect()
            return jsonify({"status": "healthy", "database": "connected"})
        else:
            return jsonify({"status": "unhealthy", "database": "disconnected"}), 500
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route('/send', methods=['POST'])
def send_message():
    """API для отправки сообщений из Access"""
    try:
        data = request.json
        to_number = data.get('to')
        message_text = data.get('message')
        
        if not to_number or not message_text:
            return jsonify({"error": "Missing required fields"}), 400
        
        if not whatsapp_api:
            return jsonify({"error": "WhatsApp API not initialized"}), 500
        
        result = whatsapp_api.send_message(to_number, message_text)
        
        if 'error' in result:
            return jsonify(result), 500
        else:
            return jsonify({"status": "sent", "result": result})
            
    except Exception as e:
        logger.error(f"Ошибка отправки сообщения: {e}")
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    # Инициализация при запуске
    initialize_whatsapp_api()
    
    # Запуск сервера
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('DEBUG', 'False').lower() == 'true'
    
    logger.info(f"Запуск WhatsApp Webhook Server на порту {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
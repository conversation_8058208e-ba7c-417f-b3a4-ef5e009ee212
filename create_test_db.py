#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Скрипт для создания тестовой базы данных SQLite
"""

import sqlite3
import os
from datetime import datetime

def create_test_database():
    """Создание тестовой базы данных SQLite"""
    db_path = 'whatsapp_test.db'
    
    # Удаляем существующую БД если есть
    if os.path.exists(db_path):
        os.remove(db_path)
        print(f"Удалена существующая БД: {db_path}")
    
    # Создаем новую БД
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print(f"Создание тестовой БД: {db_path}")
    
    # Создание таблиц
    tables_sql = [
        """
        CREATE TABLE Messages (
            MessageID TEXT PRIMARY KEY,
            ChatID TEXT NOT NULL,
            FromUser TEXT NOT NULL,
            ToUser TEXT NOT NULL,
            Text TEXT,
            MessageType TEXT,
            MediaURL TEXT,
            SentAt DATETIME NOT NULL,
            ReceivedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            Status TEXT DEFAULT 'received',
            RawData TEXT
        )
        """,
        """
        CREATE TABLE OutgoingMessages (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            ToUser TEXT NOT NULL,
            MessageText TEXT NOT NULL,
            MessageType TEXT DEFAULT 'text',
            MediaPath TEXT,
            Priority INTEGER DEFAULT 1,
            ScheduledAt DATETIME,
            CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            Status TEXT DEFAULT 'pending',
            WhatsAppMessageID TEXT,
            ErrorMessage TEXT
        )
        """,
        """
        CREATE TABLE Contacts (
            PhoneNumber TEXT PRIMARY KEY,
            ContactName TEXT,
            Company TEXT,
            Email TEXT,
            Notes TEXT,
            CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
            LastMessageAt DATETIME,
            MessageCount INTEGER DEFAULT 0,
            IsActive INTEGER DEFAULT 1
        )
        """,
        """
        CREATE TABLE Settings (
            SettingKey TEXT PRIMARY KEY,
            SettingValue TEXT NOT NULL,
            Description TEXT,
            UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """,
        """
        CREATE TABLE Logs (
            ID INTEGER PRIMARY KEY AUTOINCREMENT,
            LogLevel TEXT NOT NULL,
            Message TEXT NOT NULL,
            Source TEXT,
            CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """
    ]
    
    # Создаем таблицы
    for i, sql in enumerate(tables_sql, 1):
        cursor.execute(sql)
        print(f"Создана таблица {i}/5")
    
    # Вставляем базовые настройки
    settings_data = [
        ('whatsapp_phone_number_id', 'dummy_phone_id', 'Phone Number ID из Meta'),
        ('whatsapp_business_account_id', 'dummy_business_id', 'Business Account ID из Meta'),
        ('whatsapp_access_token', 'dummy_token', 'Access Token для API'),
        ('webhook_verify_token', 'test_verify_token_123', 'Токен для верификации webhook'),
        ('auto_reply_enabled', '0', 'Включить автоответы (0/1)'),
        ('auto_reply_message', 'Спасибо за сообщение! Мы ответим в ближайшее время.', 'Текст автоответа'),
        ('max_message_length', '1000', 'Максимальная длина сообщения')
    ]
    
    cursor.executemany(
        "INSERT INTO Settings (SettingKey, SettingValue, Description) VALUES (?, ?, ?)",
        settings_data
    )
    print("Добавлены базовые настройки")
    
    # Создаем индексы
    indexes_sql = [
        "CREATE INDEX IX_Messages_ChatID ON Messages(ChatID)",
        "CREATE INDEX IX_Messages_FromUser ON Messages(FromUser)",
        "CREATE INDEX IX_Messages_SentAt ON Messages(SentAt)",
        "CREATE INDEX IX_Messages_Status ON Messages(Status)",
        "CREATE INDEX IX_OutgoingMessages_Status ON OutgoingMessages(Status)",
        "CREATE INDEX IX_Contacts_LastMessageAt ON Contacts(LastMessageAt)",
        "CREATE INDEX IX_Logs_CreatedAt ON Logs(CreatedAt)"
    ]
    
    for sql in indexes_sql:
        cursor.execute(sql)
    print("Созданы индексы")
    
    # Добавляем тестовые данные
    test_contacts = [
        ('79009876543', 'Тестовый Контакт', 'Тест Компания', '<EMAIL>', 'Тестовые заметки'),
        ('79001234567', 'Другой Контакт', 'Другая Компания', '<EMAIL>', 'Другие заметки')
    ]
    
    cursor.executemany(
        "INSERT INTO Contacts (PhoneNumber, ContactName, Company, Email, Notes) VALUES (?, ?, ?, ?, ?)",
        test_contacts
    )
    print("Добавлены тестовые контакты")
    
    # Сохраняем изменения
    conn.commit()
    conn.close()
    
    print(f"\n✅ Тестовая база данных создана: {os.path.abspath(db_path)}")
    print("\n📋 Структура БД:")
    print("- Messages: входящие сообщения")
    print("- OutgoingMessages: исходящие сообщения")
    print("- Contacts: контакты")
    print("- Settings: настройки")
    print("- Logs: логи")
    
    return db_path

if __name__ == '__main__':
    create_test_database()
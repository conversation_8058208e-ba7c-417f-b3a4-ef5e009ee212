# Быстрый старт WhatsApp Webhook

## 🚀 Запуск за 5 минут

### Локальная разработка

#### 1. Подготовка окружения
```bash
# Клонируйте проект
cd "C:\Users\<USER>\Documents\Воркзилла\написать код на VBA Access"

# Создайте виртуальное окружение
python -m venv venv
venv\Scripts\activate

# Установите зависимости
pip install -r requirements.txt
```

#### 2. Настройка подключения к базе
```powershell
# Установите переменную окружения
$env:ACCESS_DSN="DRIVER={SQL Server};SERVER=*************,1433;DATABASE=whatsapp_integration;UID=WhatsApp_ferroli;PWD=**********`$T"
```

#### 3. Запуск сервера
```bash
python webhook_server.py
```

#### 4. Тестирование
```bash
# Откройте в браузере или выполните в терминале:
curl http://localhost:5000/db-test
```

### Развертывание на сервере

#### Автоматическое развертывание
```bash
# Сделайте скрипты исполняемыми
chmod +x deploy_remote.sh upload_and_deploy.sh

# Запустите развертывание
./upload_and_deploy.sh
```

#### Ручная настройка
```bash
# 1. Загрузите код на сервер
scp webhook_server.py root@*************:/opt/whatsapp-webhook/

# 2. Обновите .env с правильными данными
ssh root@************* "nano /opt/whatsapp-webhook/.env"

# 3. Перезапустите сервис
ssh root@************* "supervisorctl restart whatsapp-webhook"
```

## 📋 Чек-лист настройки

### SQL Server
- [ ] Сервер доступен по адресу *************:1433
- [ ] Пользователь WhatsApp_ferroli создан
- [ ] Пароль: **********$T
- [ ] База данных whatsapp_integration создана
- [ ] Таблицы созданы (whatsapp_access_tables.sql)

### WhatsApp API
- [ ] Приложение создано в Facebook for Developers
- [ ] Phone Number ID получен
- [ ] Access Token получен
- [ ] Verify Token задан
- [ ] Webhook URL настроен
- [ ] Подписки на события активированы

### Сервер
- [ ] Python 3.8+ установлен
- [ ] ODBC Driver 18 установлен
- [ ] Supervisor настроен
- [ ] Порт 8080 открыт
- [ ] Сервис запущен

## 🔧 Быстрая диагностика

### Проверка подключений
```bash
# Тест базы данных
curl http://localhost:8080/db-test

# Тест webhook
curl "http://localhost:8080/webhook?hub.verify_token=YOUR_TOKEN&hub.challenge=test&hub.mode=subscribe"

# Статус сервиса
sudo supervisorctl status whatsapp-webhook
```

### Просмотр логов
```bash
# Логи приложения
sudo tail -f /var/log/whatsapp-webhook.log

# Логи системы
sudo journalctl -u supervisor -f
```

## 🆘 Частые проблемы

### "Login failed for user"
```sql
-- Проверьте пользователя в SQL Server
SELECT name FROM sys.sql_logins WHERE name = 'WhatsApp_ferroli';
```

### "Connection timeout"
```bash
# Проверьте сеть
nc -zv ************* 1433
```

### "Port already in use"
```bash
# Найдите процесс
sudo lsof -i :8080
# Остановите процесс
sudo kill -9 PID
```

## 📞 Контакты

- **Документация**: [DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md)
- **Подключения**: [CONNECTION_GUIDE.md](./CONNECTION_GUIDE.md)
- **Поддержка**: Создайте issue в репозитории

---

**Готово!** Ваш WhatsApp Webhook сервер должен работать на http://your-server:8080
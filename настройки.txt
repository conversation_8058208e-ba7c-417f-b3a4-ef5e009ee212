Список оставшихся настроек (только необходимое, кратко):

systemd unit (исправить и запустить на 5000)
Вынести ACCESS_DSN в файл: /etc/whatsapp-webhook.env ACCESS_DSN='DRIVER={ODBC Driver 17 for SQL Server};SERVER=localhost\SQLEXPRESS;DATABASE=whatsapp_integration;Trusted_Connection=yes'
В /etc/systemd/system/whatsapp-webhook.service:
Удалить Environment=ACCESS_DSN=...
Добавить EnvironmentFile=-/etc/whatsapp-webhook.env сразу после Environment=PORT=5000
Убедиться, что ExecStart: -b 0.0.0.0:${PORT}
Выполнить: systemctl daemon-reload && systemctl restart whatsapp-webhook && systemctl status --no-pager whatsapp-webhook
Секреты в unit (реальные значения)
WHATSAPP_ACCESS_TOKEN
WHATSAPP_PHONE_NUMBER_ID
WEBHOOK_VERIFY_TOKEN
WHATSAPP_APP_SECRET
ACCESS_API_KEY (вносить в [Service] как Environment="KEY=VALUE" или через отдельный EnvironmentFile; не использовать .env в проде)
Проверки после запуска
ss -ltnp | grep ':5000'
curl -s http://127.0.0.1:5000/health → 200
curl -s http://127.0.0.1:5000/ready → 200 (при корректном DSN и доступной БД)
Доступ к БД и ODBC
Убедиться, что на сервере установлен ODBC Driver 17 for SQL Server.
Проверить сетевой доступ до SQL Server (порт SQL Server, allowlist/Firewall).
Настройка в Meta (WhatsApp Cloud API)
Верифицировать вебхук (GET /webhook): hub.verify_token = WEBHOOK_VERIFY_TOKEN
Указать URL вебхука (путь /webhook) на вашем внешнем домене/балансере.
Проверить подпись (POST /webhook должен содержать X-Hub-Signature-256 и проходить HMAC-проверку).
Сетевая политика/Firewall
Разрешить вход на порт 5000 только из доверенной сети/прокси.
Порты 80/443/22 не трогать (соблюдено). Если нужен публичный доступ — использовать существующий реверс-прокси (без изменений 80/443 на этом этапе).
Логи и ротация
Файл: /var/log/whatsapp_webhook.log (из unit).
Опционально: добавить logrotate правило ОС, если потребуется сверх встроенной ротации.
Мониторинг/автовосстановление
systemd Restart=on-failure уже включен.
Добавить мониторинг /health и /ready в вашу систему наблюдения.
Минимальные эксплуатационные тесты
POST /send c заголовком Authorization: Bearer ACCESS_API_KEY и телом { "to": "7XXXXXXXXXX", "message": "test" } → 200 и запись в БД.
Проверить, что входящие события (POST /webhook) корректно сохраняются (таблицы Messages/Logs обновляются).
Ограничения/политики (зафиксировано)
Не трогать порты 80/443/22.
Не заходить и не изменять каталог ferroli-deploy.
После выполнения пунктов 1–3 сервис должен стабильно подняться и пройти readiness. Остальные пункты — контроль качества прод-эксплуатации (БД/Meta/сеть/мониторинг/тесты).
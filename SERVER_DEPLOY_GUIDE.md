# Пошаговая инструкция по развёртыванию на сервере

## 1. Подключение к серверу
```bash
ssh root@80.232.250.61
```

## 2. Загрузка файлов на сервер
Выполните на **локальном компьютере** (не на сервере):

```bash
# Загрузка основных файлов
scp webhook_server.py root@80.232.250.61:/tmp/
scp requirements.txt root@80.232.250.61:/tmp/
scp config.env.example root@80.232.250.61:/tmp/
scp deploy_to_server.sh root@80.232.250.61:/tmp/
scp whatsapp_access_tables.sql root@80.232.250.61:/tmp/
```

## 3. Выполнение развёртывания на сервере
После подключения к серверу:

```bash
# Переход в временную директорию
cd /tmp

# Делаем скрипт исполняемым
chmod +x deploy_to_server.sh

# Запускаем развёртывание
./deploy_to_server.sh
```

## 4. Настройка приложения
```bash
# Переход в директорию приложения
cd /opt/whatsapp-webhook

# Копирование файлов
cp /tmp/webhook_server.py .
cp /tmp/requirements.txt .
cp /tmp/config.env.example .env

# Установка зависимостей
sudo -u whatsapp ./venv/bin/pip install -r requirements.txt

# Настройка прав доступа
chown whatsapp:whatsapp webhook_server.py .env requirements.txt
chmod 600 .env
```

## 5. Настройка конфигурации
```bash
# Редактирование .env файла
nano .env
```

Заполните обязательные поля:
```env
ACCESS_DSN=your_database_dsn
WEBHOOK_VERIFY_TOKEN=your_verify_token
WHATSAPP_ACCESS_TOKEN=your_access_token
WHATSAPP_APP_SECRET=your_app_secret
ACCESS_API_KEY=your_api_key_for_security
PORT=5000
DEBUG=False
```

## 6. Запуск службы
```bash
# Запуск службы
systemctl start whatsapp-webhook

# Проверка статуса
systemctl status whatsapp-webhook

# Просмотр логов
journalctl -u whatsapp-webhook -f
```

## 7. Проверка работы
```bash
# Тест health-check
curl http://localhost/health

# Проверка Nginx
nginx -t
systemctl status nginx
```

## 8. Настройка SSL (оп
# Минимальная рабочая интеграция WhatsApp Cloud API с Microsoft Access

## Для чего это нужно

Эта инструкция поможет тебе получить входящие сообщения WhatsApp прямо в таблицу Access — без сторонних сервисов. Всё максимально просто: официальное API, бесплатные инструменты, минимум кода.

---

## Что потребуется

1. **Номер WhatsApp** — рабочий, который будет использоваться для переписки с клиентами.
2. **Доступ к Facebook Business Manager** — чтобы подключить WhatsApp API (или чтобы тебя добавили как разработчика).
3. **Документы компании** (ИНН, название, сайт) — нужны для верификации в Meta.
4. **Компьютер с интернетом** (Windows или Linux) — где будет работать промежуточный сервер.
5. **Файл базы Access (.accdb)** — или описание нужных полей (если базы ещё нет).
6. **Возможность открыть порт 443** — чтобы Meta могла отправлять сообщения на твой сервер.

---

## Как это работает (простыми словами)

1. **Meta (Facebook) отправляет новые сообщения** на твой сервер через специальный «webhook» (это просто HTTP-запрос).
2. **Промежуточный сервер** (маленькая программа на Python) принимает эти сообщения.
3. **Скрипт записывает сообщения** в таблицу Access через стандартный драйвер ODBC.

---

## Пошаговая инструкция

### 1. Регистрация и настройка WhatsApp Cloud API

- Зарегистрируйся или войди в [Facebook Business Manager](https://business.facebook.com).
- Пройди верификацию бизнеса (загрузи документы).
- Создай приложение в [Meta Developers](https://developers.facebook.com).
- Подключи продукт WhatsApp, сохрани **Phone Number ID** и **Business Account ID**.
- Получи тестовый токен доступа (Bearer Token).

### 2. Настройка промежуточного сервера

- Установи Python 3.10+ (если не установлен).
- Установи нужные библиотеки:
  ```bash
  pip install flask pyodbc python-dotenv
  ```
- Создай файл `.env` с параметрами подключения к Access (например, `ACCESS_DSN=имя_вашего_DSN`).

- Пример простого сервера на Python:
  ```python
  from flask import Flask, request
  import os, pyodbc
  from dotenv import load_dotenv

  load_dotenv()
  CONN_STR = os.getenv("ACCESS_DSN")
  conn = pyodbc.connect(CONN_STR)
  cursor = conn.cursor()

  app = Flask(__name__)

  @app.route('/webhook', methods=['GET', 'POST'])
  def webhook():
      if request.method == 'GET':
          if request.args.get('hub.verify_token') == 'ВАШ_ТОКЕН':
              return request.args.get('hub.challenge')
          return 'Error', 403
      data = request.json
      # Пример вставки сообщения в Access
      for entry in data.get('entry', []):
          for change in entry.get('changes', []):
              msg = change['value'].get('messages', [{}])[0]
              if msg:
                  cursor.execute(
                      """INSERT INTO Messages (MessageID, ChatID, FromUser, Text, SentAt)
                      VALUES (?, ?, ?, ?, ?)""",
                      msg['id'], msg['from'], msg['from'], msg.get('text', {}).get('body',''), msg['timestamp']
                  )
                  conn.commit()
      return '', 200

  if __name__ == '__main__':
      app.run(host='0.0.0.0', port=443, ssl_context=('cert.pem', 'key.pem'))
  ```

  > **Важно:** Для работы на 443 порту нужен SSL-сертификат (можно самоподписанный для теста).

### 3. Настройка Access

- Создай таблицу `Messages` с нужными полями (MessageID, ChatID, FromUser, Text, SentAt).
- Настрой ODBC-подключение к базе (через Панель управления → Источники данных ODBC).
- Проверь, что можешь подключиться к Access из Python.

### 4. Проверка работы

- Зарегистрируй webhook-URL в настройках приложения Meta (укажи свой домен/IP и verify token).
- Отправь тестовое сообщение на WhatsApp — оно должно появиться в таблице Access.

---

## Вопросы и ответы

**Q: Нужно ли держать сервер включённым?**  
A: Да, чтобы получать сообщения в реальном времени, сервер должен быть всегда доступен.

**Q: Можно ли сделать без интернета?**  
A: Нет, Meta отправляет сообщения только через интернет.

**Q: Можно ли отправлять сообщения из Access?**  
A: Да, но для этого потребуется добавить отдельный скрипт, который будет делать POST-запросы к Cloud API.

---

## Итог

- Всё, что нужно — номер, доступ к Meta, база Access и компьютер с интернетом.
- Минимальный код — 20 строк на Python.
- Всё официально, бесплатно (до 1000 сообщений в месяц).

---

**Если что-то не понятно — спрашивай!**
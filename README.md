# SQL таблицы для интеграции WhatsApp с Microsoft Access

## Описание

Этот архив содержит SQL-скрипты для создания всех необходимых таблиц для интеграции WhatsApp Cloud API с Microsoft Access.

## 📦 Готовый архив для скачивания

Архив `whatsapp_access_integration_tables.zip` содержит все необходимые файлы:
- `whatsapp_access_tables.sql` - основной SQL-скрипт
- `README.md` - документация (этот файл)
- `sample_data.sql` - примеры тестовых данных

## Содержимое

### Основные таблицы:
- **Messages** - входящие сообщения WhatsApp
- **OutgoingMessages** - исходящие сообщения для отправки
- **Contacts** - информация о контактах
- **Settings** - настройки интеграции
- **Logs** - логирование операций

### Дополнительные объекты:
- Индексы для оптимизации запросов
- Представления для удобного просмотра данных

## Установка

1. Откройте SQL Server Management Studio или другой SQL-клиент
2. Подключитесь к вашей базе данных
3. Выполните скрипт `whatsapp_access_tables.sql`

## Структура таблиц

### Messages (Входящие сообщения)
- MessageID - уникальный ID сообщения от WhatsApp
- ChatID - ID чата/диалога
- FromUser - номер отправителя
- ToUser - номер получателя
- Text - текст сообщения
- MessageType - тип сообщения
- MediaURL - URL медиафайла
- SentAt - время отправки
- ReceivedAt - время получения на сервере
- Status - статус обработки
- RawData - полные данные от API

### OutgoingMessages (Исходящие сообщения)
- ID - автоинкрементный ID
- ToUser - номер получателя
- MessageText - текст сообщения
- MessageType - тип сообщения
- MediaPath - путь к медиафайлу
- Priority - приоритет отправки
- ScheduledAt - время запланированной отправки
- CreatedAt - время создания записи
- Status - статус (pending/sent/failed)
- WhatsAppMessageID - ID в WhatsApp после отправки
- ErrorMessage - сообщение об ошибке

### Contacts (Контакты)
- PhoneNumber - номер телефона (первичный ключ)
- ContactName - имя контакта
- Company - компания
- Email - email
- Notes - заметки
- CreatedAt - дата создания
- LastMessageAt - время последнего сообщения
- MessageCount - количество сообщений
- IsActive - активен ли контакт

### Settings (Настройки)
- SettingKey - ключ настройки
- SettingValue - значение настройки
- Description - описание
- UpdatedAt - время обновления

### Logs (Логи)
- ID - автоинкрементный ID
- LogLevel - уровень лога (INFO/WARNING/ERROR)
- Message - текст сообщения
- Source - источник
- CreatedAt - время создания

## Представления

### vw_RecentMessages
Показывает последние сообщения с именами контактов

### vw_UnreadMessages
Показывает непрочитанные сообщения

### vw_ContactStats
Показывает статистику по контактам

## Настройка

После создания таблиц заполните настройки в таблице Settings:
- whatsapp_phone_number_id
- whatsapp_business_account_id
- whatsapp_access_token
- webhook_verify_token

## Использование с Python

Эти таблицы совместимы с Python-скриптом из спецификации проекта. Используйте ODBC-подключение для работы с Access.

## Поддержка

При возникновении вопросов обратитесь к основной спецификации проекта.

## 📥 Скачивание

Архив `whatsapp_access_integration_tables.zip` готов для скачивания и содержит все необходимые файлы для быстрого старта интеграции WhatsApp с Microsoft Access. 
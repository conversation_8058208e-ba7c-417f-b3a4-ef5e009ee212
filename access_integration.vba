' =====================================================
' VBA модуль для интеграции WhatsApp с Microsoft Access
' =====================================================

Option Explicit

' Константы для API
Private Const WEBHOOK_URL As String = "http://localhost:5000"
Private Const SEND_ENDPOINT As String = "/send"
Private Const HEALTH_ENDPOINT As String = "/health"

' Типы данных для работы с JSON
Private Type WhatsAppMessage
    ToUser As String
    MessageText As String
    MessageType As String
    Priority As Integer
    ScheduledAt As Date
End Type

Private Type ContactInfo
    PhoneNumber As String
    ContactName As String
    Company As String
    Email As String
    Notes As String
End Type

' =====================================================
' Основные функции для работы с WhatsApp
' =====================================================

Public Function SendWhatsAppMessage(ByVal phoneNumber As String, ByVal messageText As String) As Boolean
    """
    Отправка сообщения через WhatsApp API
    
    Args:
        phoneNumber: Номер телефона получателя (без кода страны)
        messageText: Текст сообщения
        
    Returns:
        Boolean: True если сообщение отправлено успешно
    """
    On Error GoTo ErrorHandler
    
    Dim httpRequest As Object
    Set httpRequest = CreateObject("MSXML2.XMLHTTP")
    
    Dim jsonData As String
    jsonData = "{""to"": """ & phoneNumber & """, ""message"": """ & messageText & """}"
    
    ' Отправка POST запроса
    httpRequest.Open "POST", WEBHOOK_URL & SEND_ENDPOINT, False
    httpRequest.setRequestHeader "Content-Type", "application/json"
    httpRequest.send jsonData
    
    ' Проверка ответа
    If httpRequest.Status = 200 Then
        SendWhatsAppMessage = True
        LogMessage "INFO", "Сообщение отправлено: " & phoneNumber, "VBA"
    Else
        SendWhatsAppMessage = False
        LogMessage "ERROR", "Ошибка отправки: " & httpRequest.responseText, "VBA"
    End If
    
    Exit Function
    
ErrorHandler:
    SendWhatsAppMessage = False
    LogMessage "ERROR", "Ошибка в SendWhatsAppMessage: " & Err.Description, "VBA"
End Function

Public Function CheckServerHealth() As Boolean
    """
    Проверка состояния webhook сервера
    
    Returns:
        Boolean: True если сервер работает
    """
    On Error GoTo ErrorHandler
    
    Dim httpRequest As Object
    Set httpRequest = CreateObject("MSXML2.XMLHTTP")
    
    httpRequest.Open "GET", WEBHOOK_URL & HEALTH_ENDPOINT, False
    httpRequest.send
    
    CheckServerHealth = (httpRequest.Status = 200)
    
    Exit Function
    
ErrorHandler:
    CheckServerHealth = False
End Function

' =====================================================
' Функции для работы с базой данных
' =====================================================

Public Function SaveOutgoingMessage(ByVal phoneNumber As String, ByVal messageText As String, _
                                   Optional ByVal messageType As String = "text", _
                                   Optional ByVal priority As Integer = 1, _
                                   Optional ByVal scheduledAt As Date = #1/1/1900#) As Boolean
    """
    Сохранение исходящего сообщения в базу данных
    
    Args:
        phoneNumber: Номер получателя
        messageText: Текст сообщения
        messageType: Тип сообщения
        priority: Приоритет отправки
        scheduledAt: Время запланированной отправки
        
    Returns:
        Boolean: True если сообщение сохранено
    """
    On Error GoTo ErrorHandler
    
    Dim sql As String
    sql = "INSERT INTO OutgoingMessages (ToUser, MessageText, MessageType, Priority, ScheduledAt, Status) " & _
          "VALUES (?, ?, ?, ?, ?, 'pending')"
    
    Dim db As DAO.Database
    Set db = CurrentDb
    
    Dim qdf As DAO.QueryDef
    Set qdf = db.CreateQueryDef("")
    qdf.SQL = sql
    qdf.Parameters(0) = phoneNumber
    qdf.Parameters(1) = messageText
    qdf.Parameters(2) = messageType
    qdf.Parameters(3) = priority
    
    If scheduledAt = #1/1/1900# Then
        qdf.Parameters(4) = Null
    Else
        qdf.Parameters(4) = scheduledAt
    End If
    
    qdf.Execute
    SaveOutgoingMessage = True
    
    LogMessage "INFO", "Исходящее сообщение сохранено: " & phoneNumber, "VBA"
    Exit Function
    
ErrorHandler:
    SaveOutgoingMessage = False
    LogMessage "ERROR", "Ошибка сохранения исходящего сообщения: " & Err.Description, "VBA"
End Function

Public Function GetUnreadMessages() As DAO.Recordset
    """
    Получение непрочитанных сообщений
    
    Returns:
        Recordset: Набор записей с непрочитанными сообщениями
    """
    On Error GoTo ErrorHandler
    
    Dim sql As String
    sql = "SELECT * FROM vw_UnreadMessages ORDER BY SentAt DESC"
    
    Set GetUnreadMessages = CurrentDb.OpenRecordset(sql)
    Exit Function
    
ErrorHandler:
    Set GetUnreadMessages = Nothing
    LogMessage "ERROR", "Ошибка получения непрочитанных сообщений: " & Err.Description, "VBA"
End Function

Public Function GetRecentMessages(Optional ByVal limit As Integer = 50) As DAO.Recordset
    """
    Получение последних сообщений
    
    Args:
        limit: Количество сообщений для получения
        
    Returns:
        Recordset: Набор записей с сообщениями
    """
    On Error GoTo ErrorHandler
    
    Dim sql As String
    sql = "SELECT TOP " & limit & " * FROM vw_RecentMessages ORDER BY SentAt DESC"
    
    Set GetRecentMessages = CurrentDb.OpenRecordset(sql)
    Exit Function
    
ErrorHandler:
    Set GetRecentMessages = Nothing
    LogMessage "ERROR", "Ошибка получения последних сообщений: " & Err.Description, "VBA"
End Function

Public Function AddContact(ByVal contactInfo As ContactInfo) As Boolean
    """
    Добавление нового контакта
    
    Args:
        contactInfo: Информация о контакте
        
    Returns:
        Boolean: True если контакт добавлен
    """
    On Error GoTo ErrorHandler
    
    Dim sql As String
    sql = "INSERT INTO Contacts (PhoneNumber, ContactName, Company, Email, Notes) " & _
          "VALUES (?, ?, ?, ?, ?)"
    
    Dim db As DAO.Database
    Set db = CurrentDb
    
    Dim qdf As DAO.QueryDef
    Set qdf = db.CreateQueryDef("")
    qdf.SQL = sql
    qdf.Parameters(0) = contactInfo.PhoneNumber
    qdf.Parameters(1) = contactInfo.ContactName
    qdf.Parameters(2) = contactInfo.Company
    qdf.Parameters(3) = contactInfo.Email
    qdf.Parameters(4) = contactInfo.Notes
    
    qdf.Execute
    AddContact = True
    
    LogMessage "INFO", "Контакт добавлен: " & contactInfo.PhoneNumber, "VBA"
    Exit Function
    
ErrorHandler:
    AddContact = False
    LogMessage "ERROR", "Ошибка добавления контакта: " & Err.Description, "VBA"
End Function

Public Function UpdateMessageStatus(ByVal messageId As String, ByVal status As String) As Boolean
    """
    Обновление статуса сообщения
    
    Args:
        messageId: ID сообщения
        status: Новый статус
        
    Returns:
        Boolean: True если статус обновлен
    """
    On Error GoTo ErrorHandler
    
    Dim sql As String
    sql = "UPDATE Messages SET Status = ? WHERE MessageID = ?"
    
    Dim db As DAO.Database
    Set db = CurrentDb
    
    Dim qdf As DAO.QueryDef
    Set qdf = db.CreateQueryDef("")
    qdf.SQL = sql
    qdf.Parameters(0) = status
    qdf.Parameters(1) = messageId
    
    qdf.Execute
    UpdateMessageStatus = True
    
    LogMessage "INFO", "Статус сообщения обновлен: " & messageId & " -> " & status, "VBA"
    Exit Function
    
ErrorHandler:
    UpdateMessageStatus = False
    LogMessage "ERROR", "Ошибка обновления статуса: " & Err.Description, "VBA"
End Function

' =====================================================
' Функции логирования
' =====================================================

Public Function LogMessage(ByVal level As String, ByVal message As String, ByVal source As String)
    """
    Запись сообщения в лог
    
    Args:
        level: Уровень лога (INFO, WARNING, ERROR)
        message: Текст сообщения
        source: Источник сообщения
    """
    On Error GoTo ErrorHandler
    
    Dim sql As String
    sql = "INSERT INTO Logs (LogLevel, Message, Source) VALUES (?, ?, ?)"
    
    Dim db As DAO.Database
    Set db = CurrentDb
    
    Dim qdf As DAO.QueryDef
    Set qdf = db.CreateQueryDef("")
    qdf.SQL = sql
    qdf.Parameters(0) = level
    qdf.Parameters(1) = message
    qdf.Parameters(2) = source
    
    qdf.Execute
    Exit Function
    
ErrorHandler:
    ' Если не удалось записать в БД, выводим в Immediate Window
    Debug.Print "[" & level & "] " & source & ": " & message
End Function

' =====================================================
' Функции для работы с настройками
' =====================================================

Public Function GetSetting(ByVal key As String) As String
    """
    Получение настройки из базы данных
    
    Args:
        key: Ключ настройки
        
    Returns:
        String: Значение настройки
    """
    On Error GoTo ErrorHandler
    
    Dim sql As String
    sql = "SELECT SettingValue FROM Settings WHERE SettingKey = ?"
    
    Dim db As DAO.Database
    Set db = CurrentDb
    
    Dim qdf As DAO.QueryDef
    Set qdf = db.CreateQueryDef("")
    qdf.SQL = sql
    qdf.Parameters(0) = key
    
    Dim rs As DAO.Recordset
    Set rs = qdf.OpenRecordset
    
    If Not rs.EOF Then
        GetSetting = rs.Fields("SettingValue").Value
    Else
        GetSetting = ""
    End If
    
    rs.Close
    Exit Function
    
ErrorHandler:
    GetSetting = ""
End Function

Public Function SetSetting(ByVal key As String, ByVal value As String, Optional ByVal description As String = "") As Boolean
    """
    Установка настройки в базу данных
    
    Args:
        key: Ключ настройки
        value: Значение настройки
        description: Описание настройки
        
    Returns:
        Boolean: True если настройка установлена
    """
    On Error GoTo ErrorHandler
    
    Dim sql As String
    sql = "UPDATE Settings SET SettingValue = ?, UpdatedAt = Now() WHERE SettingKey = ?"
    
    Dim db As DAO.Database
    Set db = CurrentDb
    
    Dim qdf As DAO.QueryDef
    Set qdf = db.CreateQueryDef("")
    qdf.SQL = sql
    qdf.Parameters(0) = value
    qdf.Parameters(1) = key
    
    qdf.Execute
    
    ' Если записей не обновлено, добавляем новую
    If qdf.RecordsAffected = 0 Then
        sql = "INSERT INTO Settings (SettingKey, SettingValue, Description) VALUES (?, ?, ?)"
        qdf.SQL = sql
        qdf.Parameters(0) = key
        qdf.Parameters(1) = value
        qdf.Parameters(2) = description
        qdf.Execute
    End If
    
    SetSetting = True
    Exit Function
    
ErrorHandler:
    SetSetting = False
End Function

' =====================================================
' Вспомогательные функции
' =====================================================

Public Function FormatPhoneNumber(ByVal phoneNumber As String) As String
    """
    Форматирование номера телефона
    
    Args:
        phoneNumber: Номер телефона
        
    Returns:
        String: Отформатированный номер
    """
    ' Убираем все нецифровые символы
    Dim cleanNumber As String
    cleanNumber = Replace(phoneNumber, " ", "")
    cleanNumber = Replace(cleanNumber, "-", "")
    cleanNumber = Replace(cleanNumber, "(", "")
    cleanNumber = Replace(cleanNumber, ")", "")
    cleanNumber = Replace(cleanNumber, "+", "")
    
    ' Если номер начинается с 8, заменяем на 7
    If Left(cleanNumber, 1) = "8" Then
        cleanNumber = "7" & Mid(cleanNumber, 2)
    End If
    
    FormatPhoneNumber = cleanNumber
End Function

Public Function IsValidPhoneNumber(ByVal phoneNumber As String) As Boolean
    """
    Проверка корректности номера телефона
    
    Args:
        phoneNumber: Номер телефона
        
    Returns:
        Boolean: True если номер корректный
    """
    Dim cleanNumber As String
    cleanNumber = FormatPhoneNumber(phoneNumber)
    
    ' Проверяем длину (10-15 цифр)
    If Len(cleanNumber) < 10 Or Len(cleanNumber) > 15 Then
        IsValidPhoneNumber = False
        Exit Function
    End If
    
    ' Проверяем, что все символы - цифры
    Dim i As Integer
    For i = 1 To Len(cleanNumber)
        If Not IsNumeric(Mid(cleanNumber, i, 1)) Then
            IsValidPhoneNumber = False
            Exit Function
        End If
    Next i
    
    IsValidPhoneNumber = True
End Function 
# WhatsApp Integration с SQL Server Express

## 🚀 Переход с Access на SQL Server Express

Этот проект теперь полностью поддерживает **SQL Server Express** вместо Access .mdb файлов, что обеспечивает:

- ✅ Лучшую надёжность и производительность
- ✅ Поддержку до 10 ГБ данных
- ✅ Многопользовательский доступ
- ✅ Встроенные функции JSON
- ✅ Расширенные возможности индексирования

## 📋 Требования

### Системные требования
- Windows 10/11 или Windows Server
- Python 3.8+
- SQL Server Express 2019 или новее
- ODBC Driver 17 for SQL Server

### Python зависимости
```bash
pip install -r requirements.txt
```

## 🔧 Настройка SQL Server Express

### 1. Установка SQL Server Express

1. Скачайте SQL Server Express с официального сайта Microsoft
2. Установите с настройками по умолчанию
3. Запомните имя экземпляра (обычно `SQLEXPRESS`)

### 2. Настройка SQL Server Configuration Manager

1. Откройте **SQL Server Configuration Manager**
2. Перейдите в **SQL Server Network Configuration** → **Protocols for SQLEXPRESS**
3. Включите **TCP/IP** протокол
4. В свойствах TCP/IP установите порт **1433** (опционально)
5. Перезапустите службу **SQL Server (SQLEXPRESS)**

### 3. Настройка аутентификации

#### Вариант A: SQL Server Authentication
```sql
-- Включение смешанной аутентификации
ALTER LOGIN sa ENABLE;
ALTER LOGIN sa WITH PASSWORD = 'YourStrongPassword123!';
```

#### Вариант B: Windows Authentication (рекомендуется)
Используйте текущего пользователя Windows без пароля.

### 4. Создание базы данных

```sql
CREATE DATABASE whatsapp_integration;
USE whatsapp_integration;
```

Затем выполните скрипт `whatsapp_access_tables.sql` для создания таблиц.

## ⚙️ Настройка подключения

### Обновите файл .env

```env
# SQL Server Express подключение

# Вариант 1: SQL Server Authentication
ACCESS_DSN=DRIVER={ODBC Driver 17 for SQL Server};SERVER=localhost\SQLEXPRESS;DATABASE=whatsapp_integration;UID=sa;PWD=YourStrongPassword123!

# Вариант 2: Windows Authentication (рекомендуется)
ACCESS_DSN=DRIVER={ODBC Driver 17 for SQL Server};SERVER=localhost\SQLEXPRESS;DATABASE=whatsapp_integration;Trusted_Connection=yes

# Вариант 3: Удаленный сервер
ACCESS_DSN=DRIVER={ODBC Driver 17 for SQL Server};SERVER=*************\SQLEXPRESS;DATABASE=whatsapp_integration;UID=sa;PWD=YourStrongPassword123!

# WhatsApp Business API
WHATSAPP_ACCESS_TOKEN=your_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id
WHATSAPP_APP_SECRET=your_app_secret
WHATSAPP_VERIFY_TOKEN=your_verify_token

# Безопасность
ACCESS_API_KEY=your_secure_api_key

# Сервер
PORT=5000
DEBUG=False
```

## 🧪 Тестирование подключения

### Из командной строки Windows:
```cmd
sqlcmd -S localhost\SQLEXPRESS -d whatsapp_integration -E
```

### Из Python:
```python
import pyodbc

conn_str = "DRIVER={ODBC Driver 17 for SQL Server};SERVER=localhost\\SQLEXPRESS;DATABASE=whatsapp_integration;Trusted_Connection=yes"
conn = pyodbc.connect(conn_str)
cursor = conn.cursor()
cursor.execute("SELECT @@VERSION")
print(cursor.fetchone()[0])
```

## 🚀 Запуск сервера

```bash
python webhook_server.py
```

## 📊 Новые API эндпоинты

### Проверка состояния (расширенная)
```http
GET /health
```

Возвращает детальную информацию о состоянии SQL Server, размере базы данных и статистике таблиц.

### Отправка сообщений (обновленная)
```http
POST /send
Content-Type: application/json

# Текстовое сообщение
{
    "to": "+**********",
    "type": "text",
    "message": "Привет!"
}

# Шаблонное сообщение
{
    "to": "+**********",
    "type": "template",
    "template_name": "hello_world",
    "language_code": "ru",
    "parameters": ["Иван"]
}
```

### Статистика
```http
GET /stats
```

Возвращает статистику сообщений, контактов и активности.

### Обслуживание базы данных
```http
POST /maintenance
Content-Type: application/json

{
    "task": "all"  // или "cleanup_logs", "update_contact_stats", "cleanup_old_messages"
}
```

## 🔒 Безопасность

### Брандмауэр Windows
Разрешите входящие подключения на порт **1433** (SQL Server) и **5000** (Flask).

### Сетевая безопасность
- Используйте сильные пароли для SQL Server
- Ограничьте доступ к серверу по IP-адресам
- Включите шифрование соединений

## 📈 Производительность

### Индексы
Скрипт `whatsapp_access_tables.sql` автоматически создает оптимизированные индексы для:
- Поиска сообщений по дате и направлению
- Фильтрации контактов
- Быстрого доступа к настройкам
- Эффективного логирования

### Обслуживание
Регулярно запускайте задачи обслуживания через `/maintenance` для:
- Очистки старых логов
- Обновления статистики индексов
- Удаления устаревших данных

## 🔧 Устранение неполадок

### Ошибка подключения
1. Проверьте, запущена ли служба SQL Server (SQLEXPRESS)
2. Убедитесь, что TCP/IP протокол включен
3. Проверьте настройки брандмауэра
4. Убедитесь в правильности строки подключения

### Ошибки аутентификации
1. Проверьте режим аутентификации SQL Server
2. Убедитесь, что пользователь `sa` включен (для SQL Auth)
3. Проверьте права доступа к базе данных

### Проблемы с производительностью
1. Запустите обновление статистики через `/maintenance`
2. Проверьте размер логов и очистите старые записи
3. Убедитесь, что индексы созданы корректно

## 📞 Поддержка

При возникновении проблем:
1. Проверьте логи в файле `whatsapp_webhook.log`
2. Используйте эндпоинт `/health` для диагностики
3. Проверьте таблицу `Logs` в базе данных для детальной информации
# Подробный план вывода WhatsApp-Access интеграции в продакшен

> Цель: за два спринта (≈4 недели) вывести систему в стабильную прод-среду с мониторингом, резервным копированием и безопасной конфигурацией.

---

## Шаг 0. Актуальное состояние
- Репозиторий содержит: VBA-код Access, Flask-сервер, SQL-скрипты, черновые заметки в `notes.md`.
- Тесты минимальны, конфигурация частично захардкожена.
- Прод-среды (SQL Server + Windows Server) доступны, но пусты.

---

## Шаг 1. <PERSON>ы<PERSON><PERSON><PERSON> конфигурации и секретов (день 1–2)
1. Создать `.env` на сервере (пример — `config.env.example`) с:
   - `DB_CONNSTRING`, `WEBHOOK_VERIFY_TOKEN`, `SIGNING_SECRET`, `FLASK_PORT`, `LOG_LEVEL`.
2. В Access — таблица `Settings(Key, Value)`; добавить макрос для начального наполнения.
3. Удалить хардкод из VBA (`WEBHOOK_URL`, тайм-ауты) — читать `Settings` через `DLookup`.
4. Обновить `README` раздел «Configuration».

## Шаг 2. Миграции и подготовка БД (день 2–3)
1. Запустить `whatsapp_access_tables.sql` на прод-SQL Server.
2. Добавить таблицу `SchemaVersions` и записать версию `2025-08-01`.
3. Создать скрипт `migrate.ps1`:
   - проверяет текущую версию;
   - применяет новые `.sql` из папки `migrations/`.
4. Настроить письменный бэкап БД (еженощной компрессированный `.bak`).

## Шаг 3. Docker/WSGI деплой Flask-сервера (день 3–5)
1. Создать `Dockerfile.prod` (gunicorn + gevent).
2. Настроить `docker-compose.prod.yml` (app + reverse-proxy Nginx c HTTPS).
3. Добавить health-чек `GET /healthz` и `/metrics` (Prometheus).
4. CI/CD (GitHub Actions):
   - билд образа;
   - пуш в registry;
   - деплой на сервер через `ssh deploy_user@host docker pull && compose up -d`.

## Шаг 4. Очередь исходящих сообщений (день 5–6)
1. Таблица `OutgoingMessages(status, attempt_count, next_attempt_at)`.
2. Python-воркер (`send_queue_worker.py`) запускается через systemd/pm2.
3. Логика: чтение N сообщений, отправка, обновление статуса.
4. Добавить экспоненциальный backoff и DLQ (dead-letter) статус.

## Шаг 5. Логирование и мониторинг (день 6–7)
1. Python: логгер `structlog` JSON в stdout; сбор через Loki.
2. VBA: процедура `LogMessage` → таблица `Logs`; nightly экспорт CSV в S3.
3. Grafana дашборды: RPS, ошибки, latency, размер очереди.

## Шаг 6. Безопасность вебхука (день 7–8)
1. HMAC-подпись (`X-Signature`) — проверка в Flask до парсинга JSON.
2. Rate Limiter (Nginx `limit_req` или Flask-Limiter 500 rpm IP).
3. Обновить документацию WhatsApp для токенов.

## Шаг 7. Тестирование и идемпотентность (день 8–10)
1. Pytest: покрыть вебхук, воркер, DAL (SQLite in-memory).
2. Rubberduck тесты VBA: `SendWhatsAppMessage`, `LogMessage`.
3. Уникальный индекс `UNIQUE(message_id)` — защита от дублей.

## Шаг 8. Резервные копии и rollback (день 10–11)
1. Снимки Access-файла — копия на сетевое хранилище каждые 4 ч.
2. Docker image retention — хранить 3 последних тега.
3. План отката: `docker compose down && docker compose up -d image:tag-1` + restore DB.

## Шаг 9. Документация и hand-off (день 11–12)
1. `PRODUCTION_README.md` — структура сервисов, переменные окружения.
2. `RUNBOOK.md` — что делать при алертах: ошибка очереди, превышен latency.
3. Скринкаст 5 минут для саппорта (YouTube unlisted).

---

## Финальный чек-лист готовности
- [ ] Все переменные вынесены в `.env`/`Settings`
- [ ] Таблицы созданы, миграции проходят clean
- [ ] CI/CD — зелёная сборка
- [ ] `/healthz` OK, `/metrics` видны в Prometheus
- [ ] Очередь сообщений отправляет тестовые данные
- [ ] Логи попадают в Loki/таблицу
- [ ] Графаны отображают дашборды без ошибок
- [ ] Бэкапы проверены восстановлением
- [ ] Документация доступна и актуальна

---

> План рассчитан как «идеальная» дорожная карта; задачи можно дробить по спринтам. Приоритеты: безопасность → надёжность → автоматизация.
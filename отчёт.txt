## Резюме развертывания
✅ Успешно развернуто:

- WhatsApp Webhook сервер запущен на порту 8080
- Supervisor настроен и управляет процессом
- Сетевое подключение к SQL Server работает (порт 1433 доступен)
- Все зависимости установлены
⚠️ Требует настройки:

1. 1.
   Настройка базы данных: Нужно обновить файл /opt/whatsapp-webhook/.env с правильными учетными данными SQL Server
2. 2.
   WhatsApp API: Нужно добавить реальные токены WhatsApp API
## Следующие шаги:
1. 1.
   Обновите файл .env с правильными данными:
   
   ```
   ssh root@80.232.250.61 "nano /opt/
   whatsapp-webhook/.env"
   ```
2. 2.
   Перезапустите сервис после обновления настроек:
   
   ```
   ssh root@80.232.250.61 "supervisorctl 
   restart whatsapp-webhook"
   ```
3. 3.
   Проверьте работу после настройки:
   
   ```
   curl http://80.232.250.61:8080/db-test
   ```
Сервер готов к работе! Нужно только обновить настройки подключения к базе данных и WhatsApp API.


echo "== Тестируем подключение к базе Ferroli_Whatsap ==" && export CS="DRIVER={ODBC Driver 18 for SQL Server};SERVER=192.168.88.24,1433;DATABASE=Ferroli_Whatsap;UID=WhatsApp_ferroli;PWD=***********$T;Encrypt=no;TrustServerCertificate=yes" && python3 /root/test_sql_conn.py

echo "== Проверяем структуру базы Ferroli_Whatsap ==" && sqlcmd -S 192.168.88.24,1433 -U WhatsApp_ferroli -P 'fJ7pWpNjjS$T' -d Ferroli_Whatsap -C -Q "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME" -h -1 
# Руководство по подключению WhatsApp Webhook системы

## Оглавление
1. [Подключение к SQL Server](#подключение-к-sql-server)
2. [Подключение к WhatsApp Business API](#подключение-к-whatsapp-business-api)
3. [Настройка сетевых подключений](#настройка-сетевых-подключений)
4. [Тестирование подключений](#тестирование-подключений)
5. [Примеры конфигураций](#примеры-конфигураций)

## Подключение к SQL Server

### Параметры подключения

Для подключения к SQL Server используется ODBC Driver 18. Основные параметры:

```
Сервер: WIN-8BBOEP81VJK (или IP: *************)
Порт: 1433
База данных: whatsapp_integration
Пользователь: WhatsApp_ferroli
Пароль: **********$T
```

### Строки подключения

#### Для локальной разработки (Windows)
```env
ACCESS_DSN=DRIVER={SQL Server};SERVER=*************,1433;DATABASE=whatsapp_integration;UID=WhatsApp_ferroli;PWD=**********$T
```

#### Для продакшн сервера (Linux)
```env
ACCESS_DSN=DRIVER={ODBC Driver 18 for SQL Server};SERVER=*************,1433;DATABASE=whatsapp_integration;UID=WhatsApp_ferroli;PWD=**********$T;TrustServerCertificate=yes;
```

### Настройка SQL Server

#### 1. Включение TCP/IP протокола

```sql
-- Проверьте, что TCP/IP включен в SQL Server Configuration Manager
-- Путь: SQL Server Network Configuration → Protocols for MSSQLSERVER → TCP/IP
```

#### 2. Настройка портов

```sql
-- Убедитесь, что SQL Server слушает порт 1433
-- В SQL Server Configuration Manager:
-- SQL Server Network Configuration → Protocols → TCP/IP → Properties → IP Addresses
-- IPAll → TCP Port: 1433
```

#### 3. Настройка аутентификации

```sql
-- Включите смешанную аутентификацию (SQL Server and Windows Authentication)
-- В SQL Server Management Studio:
-- Server Properties → Security → SQL Server and Windows Authentication mode
```

#### 4. Создание пользователя и прав

```sql
-- Подключитесь как администратор и выполните:

-- Создание логина
CREATE LOGIN WhatsApp_ferroli WITH PASSWORD = '**********$T';
GO

-- Переключение на базу данных
USE whatsapp_integration;
GO

-- Создание пользователя
CREATE USER WhatsApp_ferroli FOR LOGIN WhatsApp_ferroli;
GO

-- Предоставление прав
ALTER ROLE db_datareader ADD MEMBER WhatsApp_ferroli;
ALTER ROLE db_datawriter ADD MEMBER WhatsApp_ferroli;
ALTER ROLE db_ddladmin ADD MEMBER WhatsApp_ferroli;
GO

-- Проверка прав
SELECT 
    dp.name AS principal_name,
    dp.type_desc AS principal_type_desc,
    r.name AS role_name
FROM sys.database_role_members rm
JOIN sys.database_principals dp ON rm.member_principal_id = dp.principal_id
JOIN sys.database_principals r ON rm.role_principal_id = r.principal_id
WHERE dp.name = 'WhatsApp_ferroli';
```

### Тестирование подключения к SQL Server

#### Из Windows (локально)
```powershell
# Тест с помощью sqlcmd
sqlcmd -S *************,1433 -U WhatsApp_ferroli -P "**********$T" -d whatsapp_integration -Q "SELECT GETDATE() as CurrentTime"

# Тест с помощью PowerShell
$connectionString = "Server=*************,1433;Database=whatsapp_integration;User Id=WhatsApp_ferroli;Password=**********`$T;"
$connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
try {
    $connection.Open()
    Write-Host "Подключение успешно!" -ForegroundColor Green
    $connection.Close()
} catch {
    Write-Host "Ошибка подключения: $($_.Exception.Message)" -ForegroundColor Red
}
```

#### Из Linux (сервер)
```bash
# Установка sqlcmd (если не установлен)
curl https://packages.microsoft.com/keys/microsoft.asc | sudo apt-key add -
curl https://packages.microsoft.com/config/ubuntu/20.04/prod.list | sudo tee /etc/apt/sources.list.d/msprod.list
sudo apt update
sudo ACCEPT_EULA=Y apt install -y mssql-tools18

# Тест подключения
/opt/mssql-tools18/bin/sqlcmd -S *************,1433 -U WhatsApp_ferroli -P "**********\$T" -d whatsapp_integration -Q "SELECT GETDATE() as CurrentTime" -C

# Тест сетевого подключения
nc -zv ************* 1433
telnet ************* 1433
```

## Подключение к WhatsApp Business API

### Получение учетных данных

#### 1. Регистрация в Facebook for Developers
1. Перейдите на https://developers.facebook.com/
2. Войдите в свой аккаунт Facebook
3. Нажмите "Мои приложения" → "Создать приложение"
4. Выберите "Бизнес" → "Далее"
5. Заполните информацию о приложении

#### 2. Настройка WhatsApp Business API
1. В панели приложения добавьте продукт "WhatsApp"
2. Перейдите в раздел "WhatsApp" → "Начало работы"
3. Выберите или создайте бизнес-аккаунт WhatsApp

#### 3. Получение токенов

**Phone Number ID:**
```
Путь: WhatsApp → Начало работы → Номера телефонов
Скопируйте ID номера телефона (например: **********12345)
```

**Access Token:**
```
Путь: WhatsApp → Начало работы → Временный токен доступа
Скопируйте токен (например: EAAxxxxxxxxxxxxx)
```

**Verify Token:**
```
Создайте свой собственный токен (например: my_webhook_verify_token_2025)
Этот токен вы задаете сами для верификации webhook
```

### Настройка Webhook

#### 1. Настройка URL Webhook
```
URL: https://your-domain.com:8080/webhook
или
URL: http://your-server-ip:8080/webhook (только для тестирования)
```

#### 2. Настройка Verify Token
```
Verify Token: my_webhook_verify_token_2025
(тот же токен, что в .env файле)
```

#### 3. Подписка на события
Выберите следующие события:
- ✅ messages
- ✅ message_deliveries  
- ✅ message_reads
- ✅ message_echoes

### Примеры API запросов

#### Отправка текстового сообщения
```bash
curl -X POST \
  "https://graph.facebook.com/v18.0/YOUR_PHONE_NUMBER_ID/messages" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "messaging_product": "whatsapp",
    "to": "***********",
    "type": "text",
    "text": {
      "body": "Привет! Это тестовое сообщение."
    }
  }'
```

#### Проверка статуса сообщения
```bash
curl -X GET \
  "https://graph.facebook.com/v18.0/MESSAGE_ID" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Настройка сетевых подключений

### Требования к сети

#### Исходящие подключения (от webhook сервера)
```
- graph.facebook.com:443 (HTTPS) - для WhatsApp API
- *************:1433 (TCP) - для SQL Server
```

#### Входящие подключения (к webhook серверу)
```
- your-server:8080 (HTTP/HTTPS) - для webhook от WhatsApp
```

### Настройка файрвола

#### Ubuntu/Debian
```bash
# Разрешить входящие подключения на порт 8080
sudo ufw allow 8080/tcp

# Разрешить исходящие подключения к SQL Server
sudo ufw allow out 1433/tcp

# Разрешить исходящие HTTPS подключения
sudo ufw allow out 443/tcp

# Проверить статус
sudo ufw status
```

#### Windows Server
```powershell
# Разрешить входящие подключения на порт 8080
New-NetFirewallRule -DisplayName "WhatsApp Webhook" -Direction Inbound -Protocol TCP -LocalPort 8080 -Action Allow

# Разрешить исходящие подключения к SQL Server
New-NetFirewallRule -DisplayName "SQL Server Out" -Direction Outbound -Protocol TCP -RemotePort 1433 -Action Allow
```

### Проверка сетевых подключений

#### Проверка доступности SQL Server
```bash
# Проверка порта
nc -zv ************* 1433
telnet ************* 1433

# Проверка DNS разрешения
nslookup WIN-8BBOEP81VJK
ping WIN-8BBOEP81VJK
```

#### Проверка доступности WhatsApp API
```bash
# Проверка подключения к Facebook Graph API
curl -I https://graph.facebook.com/v18.0/me
curl -I https://graph.facebook.com

# Проверка с токеном
curl "https://graph.facebook.com/v18.0/me?access_token=YOUR_ACCESS_TOKEN"
```

## Тестирование подключений

### Автоматические тесты

Создайте скрипт для тестирования всех подключений:

```bash
#!/bin/bash
# test_connections.sh

echo "=== Тестирование подключений WhatsApp Webhook ==="

# Тест подключения к SQL Server
echo "1. Тестирование SQL Server..."
nc -zv ************* 1433
if [ $? -eq 0 ]; then
    echo "✅ SQL Server доступен"
else
    echo "❌ SQL Server недоступен"
fi

# Тест подключения к WhatsApp API
echo "2. Тестирование WhatsApp API..."
curl -s -I https://graph.facebook.com | head -1
if [ $? -eq 0 ]; then
    echo "✅ WhatsApp API доступен"
else
    echo "❌ WhatsApp API недоступен"
fi

# Тест webhook сервера
echo "3. Тестирование webhook сервера..."
curl -s http://localhost:8080/health
if [ $? -eq 0 ]; then
    echo "✅ Webhook сервер работает"
else
    echo "❌ Webhook сервер не отвечает"
fi

# Тест базы данных через приложение
echo "4. Тестирование подключения к базе через приложение..."
response=$(curl -s http://localhost:8080/db-test)
if [[ $response == *"ok":true* ]]; then
    echo "✅ Подключение к базе данных работает"
else
    echo "❌ Ошибка подключения к базе данных"
    echo "Ответ: $response"
fi

echo "=== Тестирование завершено ==="
```

### Ручные тесты

#### Тест webhook верификации
```bash
# Замените YOUR_VERIFY_TOKEN на ваш токен
curl "http://your-server:8080/webhook?hub.verify_token=YOUR_VERIFY_TOKEN&hub.challenge=test123&hub.mode=subscribe"

# Ожидаемый ответ: test123
```

#### Тест получения сообщения
```bash
# Отправьте POST запрос с тестовыми данными
curl -X POST http://your-server:8080/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "object": "whatsapp_business_account",
    "entry": [{
      "id": "WHATSAPP_BUSINESS_ACCOUNT_ID",
      "changes": [{
        "value": {
          "messaging_product": "whatsapp",
          "metadata": {
            "display_phone_number": "***********",
            "phone_number_id": "PHONE_NUMBER_ID"
          },
          "messages": [{
            "from": "***********",
            "id": "wamid.test123",
            "timestamp": "**********",
            "text": {
              "body": "Тестовое сообщение"
            },
            "type": "text"
          }]
        },
        "field": "messages"
      }]
    }]
  }'
```

## Примеры конфигураций

### Конфигурация для разработки (.env.development)
```env
# SQL Server подключение (локальная сеть)
ACCESS_DSN=DRIVER={SQL Server};SERVER=*************,1433;DATABASE=whatsapp_integration;UID=WhatsApp_ferroli;PWD=**********$T

# WhatsApp API (тестовые токены)
WHATSAPP_PHONE_NUMBER_ID=**********12345
WHATSAPP_ACCESS_TOKEN=EAAxxxxxxxxxxxxx
WHATSAPP_VERIFY_TOKEN=dev_verify_token_2025

# Flask настройки
FLASK_ENV=development
FLASK_DEBUG=True
FLASK_HOST=127.0.0.1
FLASK_PORT=5000
```

### Конфигурация для продакшена (.env.production)
```env
# SQL Server подключение (с SSL)
ACCESS_DSN=DRIVER={ODBC Driver 18 for SQL Server};SERVER=*************,1433;DATABASE=whatsapp_integration;UID=WhatsApp_ferroli;PWD=**********$T;TrustServerCertificate=yes;Encrypt=yes;

# WhatsApp API (продакшн токены)
WHATSAPP_PHONE_NUMBER_ID=**********12345
WHATSAPP_ACCESS_TOKEN=EAAxxxxxxxxxxxxx
WHATSAPP_VERIFY_TOKEN=prod_verify_token_2025

# Flask настройки
FLASK_ENV=production
FLASK_DEBUG=False
FLASK_HOST=0.0.0.0
FLASK_PORT=8080

# Логирование
LOG_LEVEL=INFO
LOG_FILE=/var/log/whatsapp-webhook.log
```

### Конфигурация Supervisor
```ini
[program:whatsapp-webhook]
command=/opt/whatsapp-webhook/venv/bin/gunicorn --bind 0.0.0.0:8080 --workers 2 --timeout 30 --keep-alive 2 webhook_server:app
directory=/opt/whatsapp-webhook
user=whatsapp
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/whatsapp-webhook.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
environment=PATH="/opt/whatsapp-webhook/venv/bin"
```

### Конфигурация Nginx (опционально)
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location /webhook {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Увеличиваем таймауты для webhook
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    location /health {
        proxy_pass http://127.0.0.1:8080;
        access_log off;
    }
}
```

## Устранение проблем с подключением

### SQL Server

#### Проблема: "Login failed for user"
```sql
-- Проверьте существование пользователя
SELECT name FROM sys.sql_logins WHERE name = 'WhatsApp_ferroli';

-- Проверьте права пользователя
USE whatsapp_integration;
SELECT 
    dp.name AS principal_name,
    r.name AS role_name
FROM sys.database_role_members rm
JOIN sys.database_principals dp ON rm.member_principal_id = dp.principal_id
JOIN sys.database_principals r ON rm.role_principal_id = r.principal_id
WHERE dp.name = 'WhatsApp_ferroli';
```

#### Проблема: "Connection timeout"
```bash
# Проверьте сетевое подключение
ping *************
telnet ************* 1433

# Проверьте настройки SQL Server
# SQL Server Configuration Manager → SQL Server Network Configuration → Protocols → TCP/IP
```

### WhatsApp API

#### Проблема: "Invalid access token"
```bash
# Проверьте токен
curl "https://graph.facebook.com/v18.0/me?access_token=YOUR_ACCESS_TOKEN"

# Обновите токен в Facebook for Developers
```

#### Проблема: "Webhook verification failed"
```bash
# Проверьте verify token в .env
# Убедитесь, что токен совпадает с настройками в Facebook
```

### Сеть

#### Проблема: "Connection refused"
```bash
# Проверьте, что сервис запущен
sudo supervisorctl status whatsapp-webhook

# Проверьте, что порт открыт
netstat -tlnp | grep 8080

# Проверьте файрвол
sudo ufw status
```

---

**Версия документации**: 1.0  
**Дата обновления**: 2025-08-05  
**Автор**: WhatsApp Integration Team
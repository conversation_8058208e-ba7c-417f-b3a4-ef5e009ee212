# 🚀 Полное руководство по развертыванию WhatsApp интеграции

## 📋 Обзор

Это руководство поможет вам развернуть WhatsApp интеграцию с Microsoft Access на продакшн сервере.

## 🛠 Подготовка

### Требования к серверу:
- Ubuntu 18.04+ или CentOS 7+
- Python 3.8+
- Nginx
- Доступ к интернету
- Открытый порт 80/443

### Локальные требования:
- SSH клиент
- Доступ к серверу по SSH

## 🚀 Быстрое развертывание (рекомендуется)

### Вариант 1: Автоматическое развертывание
```bash
# Сделать скрипт исполняемым (Linux/Mac)
chmod +x quick_deploy.sh

# Запустить быстрое развертывание
./quick_deploy.sh 80.232.250.61 root
```

### Вариант 2: Пошаговое развертывание

#### Шаг 1: Загрузка файлов
```bash
# Сделать скрипт исполняемым
chmod +x upload_to_server.sh

# Загрузить файлы на сервер
./upload_to_server.sh 80.232.250.61 root
```

#### Шаг 2: Подключение к серверу
```bash
ssh root@80.232.250.61
```

#### Шаг 3: Развертывание на сервере
```bash
cd /tmp/whatsapp-integration
./deploy_to_server.sh
```

## ⚙️ Настройка конфигурации

После развертывания отредактируйте файл конфигурации:

```bash
cd /opt/whatsapp-webhook
nano .env
```

### Обязательные настройки:
```env
# База данных Access (настройте ODBC DSN)
ACCESS_DSN=MS Access Database

# WhatsApp API токены (получите в Meta Business Manager)
WEBHOOK_VERIFY_TOKEN=your_unique_verify_token
WHATSAPP_APP_SECRET=your_app_secret_from_meta

# Настройки сервера
PORT=5000
DEBUG=False
```

### Дополнительные настройки:
```env
# SSL сертификаты (для HTTPS)
SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/key.pem

# Логирование
LOG_LEVEL=INFO
MAX_LOG_SIZE_MB=10
```

## 🗄️ Настройка базы данных

### Создание таблиц в Access/SQL Server:
```bash
# Файл с SQL скриптом уже загружен на сервер
cat /opt/whatsapp-webhook/whatsapp_access_tables.sql
```

### Настройка ODBC DSN:
1. Установите Microsoft Access Database Engine
2. Создайте System DSN с именем "MS Access Database"
3. Укажите путь к файлу .accdb

## 🔧 Управление службой

### Запуск службы:
```bash
systemctl start whatsapp-webhook
```

### Проверка статуса:
```bash
systemctl status whatsapp-webhook
```

### Просмотр логов:
```bash
# Системные логи
journalctl -u whatsapp-webhook -f

# Логи приложения
tail -f /var/log/whatsapp-webhook/webhook.log
```

### Перезапуск службы:
```bash
systemctl restart whatsapp-webhook
```

## 🧪 Тестирование

### Проверка работы сервера:
```bash
# Health check
curl http://localhost/health

# Проверка webhook endpoint
curl -X GET "http://localhost/webhook?hub.mode=subscribe&hub.verify_token=your_token&hub.challenge=test"
```

### Проверка Nginx:
```bash
nginx -t
systemctl status nginx
```

## 🔒 Настройка SSL (HTTPS)

### Получение SSL сертификата (Let's Encrypt):
```bash
# Установка Certbot
apt install certbot python3-certbot-nginx

# Получение сертификата
certbot --nginx -d yourdomain.com
```

### Обновление Nginx конфигурации для HTTPS:
```nginx
server {
    listen 443 ssl;
    server_name yourdomain.com;
    
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📱 Настройка WhatsApp Business API

### В Meta Business Manager:
1. Создайте приложение WhatsApp Business
2. Получите Phone Number ID и Access Token
3. Настройте Webhook URL: `https://yourdomain.com/webhook`
4. Установите Verify Token из вашего .env файла
5. Подпишитесь на события: messages, message_deliveries

### Проверка webhook:
Meta автоматически отправит GET запрос для верификации.

## 🔍 Мониторинг и отладка

### Полезные команды:
```bash
# Статус всех служб
systemctl status nginx whatsapp-webhook

# Использование ресурсов
htop
df -h

# Сетевые подключения
netstat -tlnp | grep :5000
netstat -tlnp | grep :80

# Логи ошибок Nginx
tail -f /var/log/nginx/error.log
```

### Типичные проблемы:

#### 1. Служба не запускается
```bash
# Проверить логи
journalctl -u whatsapp-webhook --no-pager

# Проверить конфигурацию
cd /opt/whatsapp-webhook
./venv/bin/python webhook_server.py
```

#### 2. База данных недоступна
```bash
# Проверить ODBC подключение
python3 -c "import pyodbc; print(pyodbc.drivers())"
```

#### 3. Nginx ошибки
```bash
# Проверить конфигурацию
nginx -t

# Перезапустить
systemctl restart nginx
```

## 📊 Производительность

### Рекомендуемые настройки для продакшена:

#### Gunicorn (в systemd service):
```ini
ExecStart=/opt/whatsapp-webhook/venv/bin/gunicorn -w 4 -b 127.0.0.1:5000 --timeout 30 webhook_server:app
```

#### Nginx rate limiting:
```nginx
http {
    limit_req_zone $binary_remote_addr zone=webhook:10m rate=10r/s;
    
    server {
        location /webhook {
            limit_req zone=webhook burst=20 nodelay;
            proxy_pass http://127.0.0.1:5000;
        }
    }
}
```

## 🔄 Обновление

### Обновление кода:
```bash
cd /opt/whatsapp-webhook
# Загрузите новые файлы
systemctl restart whatsapp-webhook
```

### Резервное копирование:
```bash
# Создание бэкапа
tar -czf whatsapp-backup-$(date +%Y%m%d).tar.gz /opt/whatsapp-webhook
```

## 📞 Поддержка

При возникновении проблем:
1. Проверьте логи службы
2. Убедитесь в правильности конфигурации
3. Проверьте доступность базы данных
4. Проверьте настройки WhatsApp Business API

---

## ✅ Чек-лист развертывания

- [ ] Сервер подготовлен (Ubuntu/CentOS)
- [ ] Файлы загружены на сервер
- [ ] Скрипт развертывания выполнен
- [ ] Файл .env настроен
- [ ] База данных создана и доступна
- [ ] ODBC DSN настроен
- [ ] Служба запущена и работает
- [ ] Nginx настроен и работает
- [ ] Firewall настроен
- [ ] SSL сертификат установлен (опционально)
- [ ] WhatsApp Business API настроен
- [ ] Webhook протестирован
- [ ] Мониторинг настроен

**Готово! Ваша WhatsApp интеграция развернута и готова к работе! 🎉**
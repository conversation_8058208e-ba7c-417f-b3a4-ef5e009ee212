#!/bin/bash
# Скрипт для загрузки файлов WhatsApp интеграции на сервер
# Использование: ./upload_to_server.sh [IP_АДРЕС] [ПОЛЬЗОВАТЕЛЬ]

set -e

# Параметры по умолчанию
SERVER_IP="${1:-*************}"
SERVER_USER="${2:-root}"
SERVER_PATH="/tmp/whatsapp-integration"

echo "=== Загрузка WhatsApp интеграции на сервер ==="
echo "Сервер: $SERVER_USER@$SERVER_IP"
echo "Путь: $SERVER_PATH"
echo

# Проверка доступности сервера
echo "Проверка подключения к серверу..."
if ! ssh -o ConnectTimeout=10 -o BatchMode=yes "$SERVER_USER@$SERVER_IP" exit 2>/dev/null; then
    echo "ОШИБКА: Не удается подключиться к серверу $SERVER_IP"
    echo "Проверьте:"
    echo "1. IP адрес сервера"
    echo "2. SSH ключи или пароль"
    echo "3. Доступность сервера"
    exit 1
fi

# Создание директории на сервере
echo "Создание директории на сервере..."
ssh "$SERVER_USER@$SERVER_IP" "mkdir -p $SERVER_PATH"

# Список файлов для загрузки
FILES=(
    "webhook_server.py"
    "requirements.txt"
    "config.env.example"
    "deploy_to_server.sh"
    "whatsapp_access_tables.sql"
    "access_integration.vba"
    "table_structure.md"
    "README.md"
    "sample_data.sql"
)

echo "Загрузка файлов на сервер..."
for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  ✓ Загружаю $file..."
        scp "$file" "$SERVER_USER@$SERVER_IP:$SERVER_PATH/"
    else
        echo "  ⚠ Файл $file не найден, пропускаю..."
    fi
done

# Загрузка скрипта развертывания и установка прав
echo "Настройка прав доступа..."
ssh "$SERVER_USER@$SERVER_IP" "chmod +x $SERVER_PATH/deploy_to_server.sh"

# Создание .env файла из примера
echo "Создание файла конфигурации..."
ssh "$SERVER_USER@$SERVER_IP" "cd $SERVER_PATH && cp config.env.example .env"

echo
echo "=== Загрузка завершена успешно! ==="
echo
echo "Следующие шаги:"
echo "1. Подключитесь к серверу: ssh $SERVER_USER@$SERVER_IP"
echo "2. Перейдите в директорию: cd $SERVER_PATH"
echo "3. Отредактируйте конфигурацию: nano .env"
echo "4. Запустите развертывание: ./deploy_to_server.sh"
echo
echo "Для проверки загруженных файлов:"
echo "ssh $SERVER_USER@$SERVER_IP 'ls -la $SERVER_PATH'"
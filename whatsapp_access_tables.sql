-- =====================================================
-- SQL скрипты для интеграции WhatsApp с Microsoft Access
-- =====================================================

-- Основная таблица для хранения входящих сообщений WhatsApp
CREATE TABLE Messages (
    MessageID VARCHAR(255) PRIMARY KEY,  -- Уникальный ID сообщения от WhatsApp
    ChatID VARCHAR(255) NOT NULL,        -- ID чата/диалога
    FromUser VARCHAR(255) NOT NULL,      -- Номер отправителя (без кода страны)
    ToUser VARCHAR(255) NOT NULL,        -- Номер получателя (ваш номер)
    Text TEXT,                           -- Текст сообщения
    MessageType VARCHAR(50),             -- Тип сообщения (text, image, document, etc.)
    MediaURL VARCHAR(500),               -- URL медиафайла (если есть)
    SentAt DATETIME NOT NULL,            -- Время отправки сообщения
    ReceivedAt DATETIME DEFAULT GETDATE(), -- Время получения на сервере
    Status VARCHAR(50) DEFAULT 'received', -- Статус обработки
    RawData TEXT                         -- Полные данные от WhatsApp API
);

-- Таблица для отправки сообщений из Access
CREATE TABLE OutgoingMessages (
    ID INT IDENTITY(1,1) PRIMARY KEY,
    ToUser VARCHAR(255) NOT NULL,        -- Номер получателя
    MessageText TEXT NOT NULL,           -- Текст сообщения
    MessageType VARCHAR(50) DEFAULT 'text', -- Тип сообщения
    MediaPath VARCHAR(500),              -- Путь к медиафайлу (если есть)
    Priority INT DEFAULT 1,              -- Приоритет отправки
    ScheduledAt DATETIME,                -- Время запланированной отправки
    CreatedAt DATETIME DEFAULT GETDATE(), -- Время создания записи
    Status VARCHAR(50) DEFAULT 'pending', -- Статус: pending, sent, failed
    WhatsAppMessageID VARCHAR(255),      -- ID сообщения в WhatsApp (после отправки)
    ErrorMessage TEXT                    -- Сообщение об ошибке (если есть)
);

-- Таблица для хранения информации о контактах
CREATE TABLE Contacts (
    PhoneNumber VARCHAR(255) PRIMARY KEY, -- Номер телефона (без кода страны)
    ContactName VARCHAR(255),             -- Имя контакта
    Company VARCHAR(255),                 -- Компания
    Email VARCHAR(255),                   -- Email
    Notes TEXT,                           -- Заметки
    CreatedAt DATETIME DEFAULT GETDATE(), -- Дата создания записи
    LastMessageAt DATETIME,               -- Время последнего сообщения
    MessageCount INT DEFAULT 0,           -- Количество сообщений
    IsActive BIT DEFAULT 1                -- Активен ли контакт
);

-- Таблица для хранения настроек интеграции
CREATE TABLE Settings (
    SettingKey VARCHAR(100) PRIMARY KEY,
    SettingValue TEXT NOT NULL,
    Description VARCHAR(255),
    UpdatedAt DATETIME DEFAULT GETDATE()
);

-- Таблица для логирования операций
CREATE TABLE Logs (
    ID INT IDENTITY(1,1) PRIMARY KEY,
    LogLevel VARCHAR(20) NOT NULL,       -- INFO, WARNING, ERROR
    Message TEXT NOT NULL,               -- Текст сообщения
    Source VARCHAR(100),                 -- Источник (webhook, sender, etc.)
    CreatedAt DATETIME DEFAULT GETDATE() -- Время создания записи
);

-- =====================================================
-- Вставка базовых настроек
-- =====================================================
INSERT INTO Settings (SettingKey, SettingValue, Description) VALUES
('whatsapp_phone_number_id', '', 'Phone Number ID из Meta'),
('whatsapp_business_account_id', '', 'Business Account ID из Meta'),
('whatsapp_access_token', '', 'Access Token для API'),
('webhook_verify_token', '', 'Токен для верификации webhook'),
('auto_reply_enabled', '0', 'Включить автоответы (0/1)'),
('auto_reply_message', '', 'Текст автоответа'),
('max_message_length', '1000', 'Максимальная длина сообщения');

-- =====================================================
-- Индексы для оптимизации
-- =====================================================
CREATE INDEX IX_Messages_ChatID ON Messages(ChatID);
CREATE INDEX IX_Messages_FromUser ON Messages(FromUser);
CREATE INDEX IX_Messages_SentAt ON Messages(SentAt);
CREATE INDEX IX_Messages_Status ON Messages(Status);

CREATE INDEX IX_OutgoingMessages_Status ON OutgoingMessages(Status);
CREATE INDEX IX_OutgoingMessages_ScheduledAt ON OutgoingMessages(ScheduledAt);
CREATE INDEX IX_OutgoingMessages_ToUser ON OutgoingMessages(ToUser);

CREATE INDEX IX_Contacts_LastMessageAt ON Contacts(LastMessageAt);
CREATE INDEX IX_Contacts_IsActive ON Contacts(IsActive);

CREATE INDEX IX_Logs_CreatedAt ON Logs(CreatedAt);
CREATE INDEX IX_Logs_LogLevel ON Logs(LogLevel);

-- =====================================================
-- Представления для удобства
-- =====================================================
-- Представление для просмотра последних сообщений
CREATE VIEW vw_RecentMessages AS
SELECT 
    m.MessageID,
    m.ChatID,
    m.FromUser,
    c.ContactName,
    m.Text,
    m.MessageType,
    m.SentAt,
    m.Status
FROM Messages m
LEFT JOIN Contacts c ON m.FromUser = c.PhoneNumber
ORDER BY m.SentAt DESC;

-- Представление для непрочитанных сообщений
CREATE VIEW vw_UnreadMessages AS
SELECT 
    m.MessageID,
    m.ChatID,
    m.FromUser,
    c.ContactName,
    m.Text,
    m.SentAt
FROM Messages m
LEFT JOIN Contacts c ON m.FromUser = c.PhoneNumber
WHERE m.Status = 'received'
ORDER BY m.SentAt DESC;

-- Представление для статистики по контактам
CREATE VIEW vw_ContactStats AS
SELECT 
    c.PhoneNumber,
    c.ContactName,
    c.Company,
    c.MessageCount,
    c.LastMessageAt,
    c.IsActive
FROM Contacts c
ORDER BY c.LastMessageAt DESC; 
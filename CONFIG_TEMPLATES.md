# Шаблоны конфигураций WhatsApp Webhook

## 📁 Структура конфигурационных файлов

```
project/
├── .env                    # Основная конфигурация
├── .env.example           # Пример конфигурации
├── .env.development       # Настройки для разработки
├── .env.production        # Настройки для продакшена
├── config/
│   ├── supervisor.conf    # Конфигурация Supervisor
│   ├── nginx.conf         # Конфигурация Nginx
│   └── systemd.service    # Systemd сервис
└── scripts/
    ├── deploy.sh          # Скрипт развертывания
    └── backup.sh          # Скрипт резервного копирования
```

## 🔧 Файлы окружения (.env)

### .env.example
```bash
# ===========================================
# WhatsApp Webhook Configuration Template
# ===========================================

# Сервер настройки
SERVER_HOST=0.0.0.0
SERVER_PORT=5000
DEBUG=False

# WhatsApp Business API
WHATSAPP_ACCESS_TOKEN=your_access_token_here
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id_here
WHATSAPP_VERIFY_TOKEN=your_verify_token_here
WHATSAPP_APP_SECRET=your_app_secret_here

# SQL Server подключение
SQL_SERVER=your_server_ip_or_name
SQL_DATABASE=your_database_name
SQL_USERNAME=your_username
SQL_PASSWORD=your_password
SQL_DRIVER=ODBC Driver 18 for SQL Server
SQL_TRUST_CERT=yes
SQL_ENCRYPT=no

# Логирование
LOG_LEVEL=INFO
LOG_FILE=/var/log/whatsapp-webhook.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# Безопасность
SECRET_KEY=your_secret_key_here
ALLOWED_HOSTS=localhost,127.0.0.1

# Дополнительные настройки
TIMEZONE=Europe/Moscow
MAX_MESSAGE_LENGTH=4096
RETRY_ATTEMPTS=3
RETRY_DELAY=5
```

### .env.development
```bash
# ===========================================
# Development Environment Configuration
# ===========================================

# Сервер настройки
SERVER_HOST=127.0.0.1
SERVER_PORT=5000
DEBUG=True

# WhatsApp Business API (тестовые значения)
WHATSAPP_ACCESS_TOKEN=EAAxxxxxxxxxxxxxxx
WHATSAPP_PHONE_NUMBER_ID=123456789012345
WHATSAPP_VERIFY_TOKEN=dev_verify_token_123
WHATSAPP_APP_SECRET=dev_app_secret_456

# SQL Server подключение (локальная база)
SQL_SERVER=localhost
SQL_DATABASE=WhatsAppWebhook_Dev
SQL_USERNAME=dev_user
SQL_PASSWORD=dev_password
SQL_DRIVER=ODBC Driver 18 for SQL Server
SQL_TRUST_CERT=yes
SQL_ENCRYPT=no

# Логирование (подробное для разработки)
LOG_LEVEL=DEBUG
LOG_FILE=./logs/webhook-dev.log
LOG_MAX_SIZE=5MB
LOG_BACKUP_COUNT=3

# Безопасность (слабая для разработки)
SECRET_KEY=dev_secret_key_not_for_production
ALLOWED_HOSTS=*

# Дополнительные настройки
TIMEZONE=Europe/Moscow
MAX_MESSAGE_LENGTH=4096
RETRY_ATTEMPTS=1
RETRY_DELAY=1

# Отладка
SAVE_RAW_REQUESTS=True
MOCK_WHATSAPP_API=False
```

### .env.production
```bash
# ===========================================
# Production Environment Configuration
# ===========================================

# Сервер настройки
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
DEBUG=False

# WhatsApp Business API (продакшн значения)
WHATSAPP_ACCESS_TOKEN=EAAxxxxxxxxxxxxxxx_PRODUCTION
WHATSAPP_PHONE_NUMBER_ID=987654321098765
WHATSAPP_VERIFY_TOKEN=prod_verify_token_secure_123
WHATSAPP_APP_SECRET=prod_app_secret_secure_456

# SQL Server подключение (продакшн база)
SQL_SERVER=*************
SQL_DATABASE=WhatsAppWebhook
SQL_USERNAME=WhatsApp_ferroli
SQL_PASSWORD=Ferroli2024!
SQL_DRIVER=ODBC Driver 18 for SQL Server
SQL_TRUST_CERT=yes
SQL_ENCRYPT=no

# Логирование (оптимизированное для продакшена)
LOG_LEVEL=INFO
LOG_FILE=/var/log/whatsapp-webhook.log
LOG_MAX_SIZE=50MB
LOG_BACKUP_COUNT=10

# Безопасность (строгая для продакшена)
SECRET_KEY=super_secure_production_key_change_me
ALLOWED_HOSTS=*************,yourdomain.com

# Дополнительные настройки
TIMEZONE=Europe/Moscow
MAX_MESSAGE_LENGTH=4096
RETRY_ATTEMPTS=3
RETRY_DELAY=5

# Производительность
WORKERS=4
WORKER_CONNECTIONS=1000
KEEPALIVE=2

# Мониторинг
HEALTH_CHECK_INTERVAL=30
METRICS_ENABLED=True
```

## 🔄 Supervisor конфигурация

### config/supervisor.conf
```ini
[program:whatsapp-webhook]
command=/usr/bin/python3 /opt/whatsapp-webhook/webhook_server.py
directory=/opt/whatsapp-webhook
user=webhook
group=webhook
autostart=true
autorestart=true
startretries=3
redirect_stderr=true
stdout_logfile=/var/log/supervisor/whatsapp-webhook.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
environment=PATH="/opt/whatsapp-webhook/venv/bin:%(ENV_PATH)s"

[program:whatsapp-webhook-worker]
command=/usr/bin/python3 /opt/whatsapp-webhook/worker.py
directory=/opt/whatsapp-webhook
user=webhook
group=webhook
autostart=true
autorestart=true
startretries=3
numprocs=2
process_name=%(program_name)s_%(process_num)02d
redirect_stderr=true
stdout_logfile=/var/log/supervisor/whatsapp-webhook-worker.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=5
environment=PATH="/opt/whatsapp-webhook/venv/bin:%(ENV_PATH)s"

[group:whatsapp]
programs=whatsapp-webhook,whatsapp-webhook-worker
priority=999
```

## 🌐 Nginx конфигурация

### config/nginx.conf
```nginx
# WhatsApp Webhook Nginx Configuration

upstream whatsapp_webhook {
    server 127.0.0.1:8080;
    # Для множественных воркеров:
    # server 127.0.0.1:8081;
    # server 127.0.0.1:8082;
}

server {
    listen 80;
    server_name ************* yourdomain.com;
    
    # Редирект на HTTPS (рекомендуется для продакшена)
    # return 301 https://$server_name$request_uri;

    # Логирование
    access_log /var/log/nginx/whatsapp-webhook-access.log;
    error_log /var/log/nginx/whatsapp-webhook-error.log;

    # Основной прокси
    location / {
        proxy_pass http://whatsapp_webhook;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Таймауты
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # Буферизация
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }

    # Webhook endpoint (специальные настройки)
    location /webhook {
        proxy_pass http://whatsapp_webhook;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Увеличенные таймауты для webhook
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Размер тела запроса
        client_max_body_size 10M;
        
        # Отключение буферизации для webhook
        proxy_buffering off;
    }

    # Статические файлы (если есть)
    location /static/ {
        alias /opt/whatsapp-webhook/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Проверка здоровья
    location /health {
        proxy_pass http://whatsapp_webhook;
        access_log off;
    }

    # Безопасность
    location ~ /\. {
        deny all;
    }
    
    # Ограничение доступа к служебным файлам
    location ~ \.(env|conf|config)$ {
        deny all;
    }
}

# HTTPS конфигурация (для продакшена)
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    # SSL сертификаты
    ssl_certificate /etc/ssl/certs/yourdomain.com.crt;
    ssl_certificate_key /etc/ssl/private/yourdomain.com.key;
    
    # SSL настройки
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Остальная конфигурация аналогична HTTP
    # ... (скопировать из блока выше)
}
```

## 🔧 Systemd сервис

### config/systemd.service
```ini
[Unit]
Description=WhatsApp Webhook Server
After=network.target
Wants=network.target

[Service]
Type=simple
User=webhook
Group=webhook
WorkingDirectory=/opt/whatsapp-webhook
Environment=PATH=/opt/whatsapp-webhook/venv/bin
ExecStart=/opt/whatsapp-webhook/venv/bin/python webhook_server.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
StartLimitInterval=60s
StartLimitBurst=3

# Логирование
StandardOutput=journal
StandardError=journal
SyslogIdentifier=whatsapp-webhook

# Безопасность
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/whatsapp-webhook /var/log

# Ресурсы
LimitNOFILE=65536
MemoryMax=512M
CPUQuota=200%

[Install]
WantedBy=multi-user.target
```

## 📜 Скрипты развертывания

### scripts/deploy.sh
```bash
#!/bin/bash

# ===========================================
# WhatsApp Webhook Deployment Script
# ===========================================

set -e

# Конфигурация
PROJECT_DIR="/opt/whatsapp-webhook"
BACKUP_DIR="/opt/backups/whatsapp-webhook"
SERVICE_NAME="whatsapp-webhook"
USER="webhook"
GROUP="webhook"

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Функции
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Проверка прав
if [[ $EUID -ne 0 ]]; then
   log_error "Этот скрипт должен запускаться от имени root"
   exit 1
fi

# Создание резервной копии
create_backup() {
    log_info "Создание резервной копии..."
    
    if [ -d "$PROJECT_DIR" ]; then
        BACKUP_NAME="backup_$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$BACKUP_DIR"
        cp -r "$PROJECT_DIR" "$BACKUP_DIR/$BACKUP_NAME"
        log_info "Резервная копия создана: $BACKUP_DIR/$BACKUP_NAME"
    fi
}

# Остановка сервиса
stop_service() {
    log_info "Остановка сервиса..."
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        systemctl stop "$SERVICE_NAME"
        log_info "Сервис остановлен"
    else
        log_warn "Сервис уже остановлен"
    fi
}

# Обновление кода
update_code() {
    log_info "Обновление кода..."
    
    # Здесь может быть git pull или копирование файлов
    # git -C "$PROJECT_DIR" pull origin main
    
    # Установка зависимостей
    if [ -f "$PROJECT_DIR/requirements.txt" ]; then
        "$PROJECT_DIR/venv/bin/pip" install -r "$PROJECT_DIR/requirements.txt"
    fi
    
    # Установка прав
    chown -R "$USER:$GROUP" "$PROJECT_DIR"
    chmod +x "$PROJECT_DIR"/*.py
}

# Запуск сервиса
start_service() {
    log_info "Запуск сервиса..."
    
    systemctl start "$SERVICE_NAME"
    systemctl enable "$SERVICE_NAME"
    
    # Проверка статуса
    sleep 5
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_info "Сервис успешно запущен"
    else
        log_error "Ошибка запуска сервиса"
        systemctl status "$SERVICE_NAME"
        exit 1
    fi
}

# Проверка здоровья
health_check() {
    log_info "Проверка здоровья сервиса..."
    
    for i in {1..10}; do
        if curl -s http://localhost:8080/health > /dev/null; then
            log_info "Сервис работает корректно"
            return 0
        fi
        log_warn "Попытка $i/10: сервис еще не готов"
        sleep 2
    done
    
    log_error "Сервис не отвечает на проверки здоровья"
    return 1
}

# Основной процесс развертывания
main() {
    log_info "Начало развертывания WhatsApp Webhook..."
    
    create_backup
    stop_service
    update_code
    start_service
    health_check
    
    log_info "Развертывание завершено успешно!"
}

# Запуск
main "$@"
```

### scripts/backup.sh
```bash
#!/bin/bash

# ===========================================
# WhatsApp Webhook Backup Script
# ===========================================

set -e

# Конфигурация
PROJECT_DIR="/opt/whatsapp-webhook"
BACKUP_DIR="/opt/backups/whatsapp-webhook"
DB_BACKUP_DIR="/opt/backups/database"
RETENTION_DAYS=30

# Создание директорий
mkdir -p "$BACKUP_DIR" "$DB_BACKUP_DIR"

# Резервная копия файлов
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
FILE_BACKUP="$BACKUP_DIR/files_$TIMESTAMP.tar.gz"

echo "Создание резервной копии файлов..."
tar -czf "$FILE_BACKUP" -C "$(dirname $PROJECT_DIR)" "$(basename $PROJECT_DIR)"

# Резервная копия базы данных
DB_BACKUP="$DB_BACKUP_DIR/database_$TIMESTAMP.bak"

echo "Создание резервной копии базы данных..."
# Здесь должна быть команда для создания бэкапа SQL Server
# sqlcmd -S server -d database -Q "BACKUP DATABASE [WhatsAppWebhook] TO DISK = '$DB_BACKUP'"

# Очистка старых резервных копий
echo "Очистка старых резервных копий..."
find "$BACKUP_DIR" -name "files_*.tar.gz" -mtime +$RETENTION_DAYS -delete
find "$DB_BACKUP_DIR" -name "database_*.bak" -mtime +$RETENTION_DAYS -delete

echo "Резервное копирование завершено"
echo "Файлы: $FILE_BACKUP"
echo "База данных: $DB_BACKUP"
```

## 🔍 Мониторинг конфигурации

### config/monitoring.conf
```ini
# Prometheus мониторинг
[prometheus]
enabled = true
port = 9090
metrics_path = /metrics

# Grafana дашборд
[grafana]
enabled = true
port = 3000
dashboard_path = /opt/whatsapp-webhook/monitoring/dashboard.json

# Алерты
[alerts]
enabled = true
webhook_url = https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
email_smtp = smtp.gmail.com:587
email_user = <EMAIL>
email_password = your_app_password

# Пороги для алертов
[thresholds]
response_time_ms = 5000
error_rate_percent = 5
memory_usage_percent = 80
disk_usage_percent = 85
```

## 🔐 Безопасность конфигурации

### Права доступа к файлам
```bash
# Установка правильных прав доступа
chmod 600 .env*                    # Только владелец может читать
chmod 644 config/*.conf            # Все могут читать, только владелец может писать
chmod 755 scripts/*.sh             # Исполняемые скрипты
chmod 700 /opt/whatsapp-webhook    # Только владелец имеет доступ к директории
```

### Шифрование конфиденциальных данных
```bash
# Использование ansible-vault для шифрования
ansible-vault encrypt .env.production

# Расшифровка при развертывании
ansible-vault decrypt .env.production --output .env
```

---

**Примечание**: Замените все примеры значений на реальные данные вашего проекта. Никогда не коммитьте файлы с реальными паролями и токенами в систему контроля версий.
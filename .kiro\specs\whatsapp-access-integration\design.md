# Проект интеграции WhatsApp с Microsoft Access

## Обзор

Система представляет собой трехуровневую архитектуру для интеграции WhatsApp Business Cloud API с Microsoft Access. Включает веб-сервис для получения webhook-уведомлений от Meta, промежуточный слой обработки данных и компонент записи в Access через ODBC.

## Архитектура

```mermaid
graph TD
    A[WhatsApp Business Cloud API] -->|Webhook POST| B[Flask Web Service]
    B -->|Обработка JSON| C[Message Processor]
    C -->|pyodbc| D[Microsoft Access DB]
    D -->|ODBC DSN| E[Access Forms & Reports]
    F[Config Manager] -->|Токены/настройки| B
    G[Logger] -->|Логи событий| H[Log Files]
    B --> G
    C --> G
```

### Компоненты системы

1. **Webhook Receiver** - Flask приложение для приема уведомлений
2. **Message Processor** - обработка и валидация данных от WhatsApp
3. **Database Manager** - управление ODBC соединением и записью в Access
4. **Configuration Manager** - управление токенами и настройками
5. **Logger** - система логирования и мониторинга

## Компоненты и интерфейсы

### 1. Webhook Receiver (Flask App)

**Интерфейсы:**
- `GET /webhook` - верификация webhook от Meta
- `POST /webhook` - получение событий WhatsApp
- `GET /health` - проверка состояния сервиса

**Основные методы:**
```python
def verify_webhook(hub_challenge, hub_verify_token)
def process_webhook_event(request_data)
def validate_signature(payload, signature)
```

### 2. Message Processor

**Интерфейсы:**
```python
class MessageProcessor:
    def process_message(self, message_data) -> ProcessedMessage
    def process_call(self, call_data) -> ProcessedCall  
    def download_media(self, media_id) -> MediaFile
    def validate_phone_number(self, phone) -> bool
```

### 3. Database Manager

**Интерфейсы:**
```python
class AccessDatabaseManager:
    def connect(self) -> bool
    def insert_message(self, message: ProcessedMessage) -> bool
    def insert_call(self, call: ProcessedCall) -> bool
    def check_connection(self) -> bool
    def retry_operation(self, operation, max_retries=3)
```

### 4. Configuration Manager

**Интерфейсы:**
```python
class ConfigManager:
    def load_config(self) -> Config
    def get_access_token(self) -> str
    def get_webhook_verify_token(self) -> str
    def get_database_dsn(self) -> str
    def encrypt_sensitive_data(self, data) -> str
```

## Модели данных

### Таблица Messages (Access)
```sql
CREATE TABLE Messages (
    ID AUTOINCREMENT PRIMARY KEY,
    MessageID TEXT(50) NOT NULL,
    ChatID TEXT(20) NOT NULL,
    FromUser TEXT(20) NOT NULL,
    MessageText MEMO,
    MessageType TEXT(20), -- text, image, audio, video, document
    MediaURL TEXT(255),
    Timestamp DATETIME NOT NULL,
    CreatedAt DATETIME DEFAULT NOW(),
    Status TEXT(20) DEFAULT 'received'
);
```

### Таблица Calls (Access)
```sql
CREATE TABLE Calls (
    ID AUTOINCREMENT PRIMARY KEY,
    CallID TEXT(50) NOT NULL,
    FromUser TEXT(20) NOT NULL,
    StartTime DATETIME NOT NULL,
    EndTime DATETIME,
    Duration INTEGER, -- в секундах
    Status TEXT(20), -- answered, missed, rejected
    CreatedAt DATETIME DEFAULT NOW()
);
```

### Таблица Contacts (Access)
```sql
CREATE TABLE Contacts (
    ID AUTOINCREMENT PRIMARY KEY,
    PhoneNumber TEXT(20) NOT NULL UNIQUE,
    DisplayName TEXT(100),
    LastMessageAt DATETIME,
    MessageCount INTEGER DEFAULT 0,
    CreatedAt DATETIME DEFAULT NOW()
);
```

### Python модели данных
```python
@dataclass
class ProcessedMessage:
    message_id: str
    chat_id: str
    from_user: str
    message_text: str
    message_type: str
    media_url: Optional[str]
    timestamp: datetime
    
@dataclass
class ProcessedCall:
    call_id: str
    from_user: str
    start_time: datetime
    end_time: Optional[datetime]
    duration: Optional[int]
    status: str
```

## Обработка ошибок

### Стратегии обработки ошибок

1. **Webhook ошибки**
   - Неверный JSON: возврат HTTP 400
   - Неверная подпись: возврат HTTP 403
   - Внутренняя ошибка: возврат HTTP 500, логирование

2. **База данных ошибки**
   - Потеря соединения: автоматическое переподключение (до 3 попыток)
   - Блокировка базы: ожидание до 60 секунд
   - Дублирование записей: проверка по MessageID

3. **API ошибки**
   - Истекший токен: уведомление администратора
   - Rate limiting: экспоненциальная задержка
   - Недоступность сервиса: логирование и повтор

### Retry механизм
```python
def retry_with_backoff(func, max_retries=3, base_delay=1):
    for attempt in range(max_retries):
        try:
            return func()
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            time.sleep(base_delay * (2 ** attempt))
```

## Стратегия тестирования

### Unit тесты
- Тестирование обработки JSON от WhatsApp API
- Валидация данных перед записью в базу
- Проверка ODBC соединения и операций
- Тестирование конфигурационного менеджера

### Integration тесты  
- Полный цикл webhook → обработка → запись в Access
- Тестирование с реальными данными от WhatsApp
- Проверка работы с заблокированной базой данных
- Тестирование восстановления соединения

### End-to-End тесты
- Отправка тестового сообщения через WhatsApp
- Проверка появления записи в Access
- Тестирование форм и запросов в Access
- Проверка логирования и мониторинга

### Тестовые данные
```python
SAMPLE_MESSAGE_WEBHOOK = {
    "entry": [{
        "changes": [{
            "value": {
                "messages": [{
                    "id": "wamid.test123",
                    "from": "79001234567",
                    "timestamp": "1640995200",
                    "text": {"body": "Тестовое сообщение"}
                }]
            }
        }]
    }]
}
```

## Развертывание и конфигурация

### Системные требования
- Windows Server 2016+ или Windows 10+
- Python 3.8+
- Microsoft Access 2016+ или Access Runtime
- ODBC Driver for Microsoft Access
- SSL сертификат для HTTPS

### Конфигурационный файл (.env)
```
# WhatsApp API
WHATSAPP_ACCESS_TOKEN=your_access_token_here
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WEBHOOK_VERIFY_TOKEN=your_verify_token

# Database
ACCESS_DSN=MS Access Database
ACCESS_DB_PATH=C:\path\to\database.accdb

# Server
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
SSL_CERT_PATH=cert.pem
SSL_KEY_PATH=key.pem

# Logging
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/whatsapp_integration.log
MAX_LOG_SIZE_MB=10
```

### Установка и настройка
1. Создание ODBC DSN для Access базы
2. Установка Python зависимостей
3. Настройка SSL сертификата
4. Конфигурация webhook URL в Meta Developer Console
5. Создание таблиц в Access базе данных

## Безопасность

### Защита токенов
- Хранение в зашифрованном .env файле
- Ротация токенов каждые 60 дней
- Ограничение доступа к конфигурационным файлам

### Валидация webhook
- Проверка подписи запросов от Meta
- Валидация IP адресов отправителя
- Rate limiting для защиты от DDoS

### Защита данных
- Маскирование номеров телефонов в логах
- Шифрование чувствительных данных в базе
- Регулярное резервное копирование базы Access
@echo off
echo =====================================================
echo WhatsApp Integration Test Suite
echo =====================================================
echo.

set BASE_URL=http://localhost:5000

echo 1. Проверка здоровья сервера...
curl -s %BASE_URL%/health
echo.
echo.

echo 2. Проверка подключения к базе данных...
curl -s %BASE_URL%/db-test
echo.
echo.

echo 3. Проверка верификации webhook...
curl -s "%BASE_URL%/webhook?hub.verify_token=test_verify_token_123&hub.challenge=12345&hub.mode=subscribe"
echo.
echo.

echo 4. Тест входящего сообщения (симуляция)...
curl -X POST %BASE_URL%/webhook ^
  -H "Content-Type: application/json" ^
  -d "{\"object\": \"whatsapp_business_account\", \"entry\": [{\"id\": \"TEST_ACCOUNT_ID\", \"changes\": [{\"value\": {\"messaging_product\": \"whatsapp\", \"metadata\": {\"display_phone_number\": \"***********\", \"phone_number_id\": \"TEST_PHONE_ID\"}, \"messages\": [{\"from\": \"***********\", \"id\": \"test_message_id_123\", \"timestamp\": \"**********\", \"text\": {\"body\": \"Тестовое сообщение от скрипта\"}, \"type\": \"text\"}]}, \"field\": \"messages\"}]}]}"
echo.
echo.

echo 5. Тест исходящего сообщения (TEST_MODE)...
curl -X POST %BASE_URL%/send ^
  -H "Content-Type: application/json" ^
  -d "{\"to\": \"***********\", \"message\": \"Привет! Это тестовое сообщение из TEST_MODE.\"}"
echo.
echo.

echo =====================================================
echo Тестирование завершено!
echo Проверьте логи в whatsapp_webhook.log
echo =====================================================
pause

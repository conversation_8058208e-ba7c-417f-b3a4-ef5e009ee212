# 🧪 Руководство по тестированию без аккаунта Facebook

## Обзор
Данное руководство позволяет протестировать интеграцию WhatsApp ↔ Microsoft Access без реального аккаунта Facebook, используя локальные симуляции и тестовый режим.

## Что было добавлено

### 1. Тестовый режим в webhook_server.py
- **TEST_MODE**: При `TEST_MODE=1` функция `send_message` возвращает фиктивный успешный ответ вместо обращения к Facebook API
- **Fallback на .env**: Функция `initialize_whatsapp_api` теперь использует переменные окружения, если настройки не найдены в БД

### 2. Файл .env для тестирования
- `TEST_MODE=1` - включает тестовый режим
- `DEBUG=True` - включает отладочные логи
- Dummy значения для WhatsApp API
- Пустой `WHATSAPP_APP_SECRET` для отключения проверки подписи

## Пошаговое тестирование

### Шаг 1: Подготовка окружения

1. **Настройте DSN в .env файле:**
   ```
   ACCESS_DSN=DRIVER={ODBC Driver 17 for SQL Server};SERVER=localhost\SQLEXPRESS;DATABASE=whatsapp_integration;Trusted_Connection=yes
   ```
   Замените на ваши настройки подключения к БД.

2. **Убедитесь, что таблицы созданы:**
   - Импортируйте `whatsapp_access_tables.sql` в вашу БД
   - Или убедитесь, что существуют таблицы: Logs, Messages, Contacts, Settings

### Шаг 2: Запуск сервера

```bash
python webhook_server.py
```

**Ожидаемый вывод:**
```
WhatsApp API инициализирован
Запуск WhatsApp Webhook Server на порту 5000
```

### Шаг 3: Проверка здоровья сервера

```bash
curl http://localhost:5000/health
```

**Ожидаемый ответ:**
```json
{"status": "healthy", "database": "connected"}
```

```bash
curl http://localhost:5000/db-test
```

**Ожидаемый ответ:**
```json
{"ok": true, "result": 1}
```

### Шаг 4: Тест верификации webhook

```bash
curl "http://localhost:5000/webhook?hub.verify_token=test_verify_token_123&hub.challenge=123&hub.mode=subscribe"
```

**Ожидаемый ответ:** `123`

### Шаг 5: Симуляция входящего сообщения

```bash
curl -X POST http://localhost:5000/webhook \
  -H "Content-Type: application/json" \
  -d '{
    "object": "whatsapp_business_account",
    "entry": [{
      "id": "TEST_ACCOUNT_ID",
      "changes": [{
        "value": {
          "messaging_product": "whatsapp",
          "metadata": {
            "display_phone_number": "***********",
            "phone_number_id": "***************"
          },
          "messages": [{
            "from": "***********",
            "id": "test_message_id_123",
            "timestamp": "**********",
            "text": {
              "body": "Привет! Это тестовое сообщение."
            },
            "type": "text"
          }]
        },
        "field": "messages"
      }]
    }]
  }'
```

**Проверьте:**
- В БД таблица `Messages` - должна появиться новая запись
- В БД таблица `Contacts` - должен создаться или обновиться контакт
- В файле `whatsapp_webhook.log` - должны появиться логи

### Шаг 6: Тест отправки сообщения (TEST_MODE)

```bash
curl -X POST http://localhost:5000/send \
  -H "Content-Type: application/json" \
  -d '{"to":"***********","message":"Привет из тестового режима!"}'
```

**Ожидаемый ответ:**
```json
{
  "status": "sent",
  "result": {
    "messages": [{"id": "test_**********"}],
    "to": "***********",
    "test_mode": true
  }
}
```

**В логах должно появиться:**
```
[TEST_MODE] send to ***********: Привет из тестового режима!
```

### Шаг 7: Тест из Access VBA

1. **Проверка здоровья сервера:**
   ```vba
   Debug.Print CheckServerHealth() ' Должно вернуть True
   ```

2. **Отправка сообщения:**
   ```vba
   Debug.Print SendWhatsAppMessage("***********", "Тест из Access")
   ' Должно вернуть True и в логах появится [TEST_MODE]
   ```

## Переход к продакшену

Когда появится аккаунт Facebook:

1. **Обновите .env:**
   ```
   TEST_MODE=0
   DEBUG=False
   WHATSAPP_ACCESS_TOKEN=реальный_токен
   WHATSAPP_PHONE_NUMBER_ID=реальный_id
   WHATSAPP_APP_SECRET=реальный_секрет
   ```

2. **Или добавьте настройки в БД:**
   - В таблицу `Settings` добавьте записи с ключами:
     - `whatsapp_phone_number_id`
     - `whatsapp_access_token`
     - `webhook_verify_token`

3. **Настройте реальный webhook URL, SSL, firewall**

## Устранение неполадок

### Ошибка подключения к БД
- Проверьте DSN в .env
- Убедитесь, что SQL Server запущен
- Проверьте права доступа

### Сервер не запускается
- Проверьте, что порт 5000 свободен
- Убедитесь, что установлены все зависимости: `pip install -r requirements.txt`

### TEST_MODE не работает
- Убедитесь, что `TEST_MODE=1` в .env
- Перезапустите сервер после изменения .env
- Проверьте логи на наличие ошибок инициализации

## Заключение

Теперь вы можете полностью протестировать интеграцию WhatsApp ↔ Access без реального аккаунта Facebook. Все входящие и исходящие сообщения симулируются локально, а данные сохраняются в реальную БД.